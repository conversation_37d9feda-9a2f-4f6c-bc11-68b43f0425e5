import React from 'react';
import { Check, Target, Users, Video } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { ClientPackage } from '@/types/teacher';

type FilterType = 'all' | 'birebir' | 'grup' | 'video';
type LevelType = 'all' | 'a1' | 'a2' | 'b1' | 'b2' | 'c1' | 'c2';

interface PackageTypeFilterProps {
  packages: ClientPackage[];
  activeFilter: FilterType;
  activeLevel: LevelType;
  onFilterChange: (filter: FilterType) => void;
  onLevelChange: (level: LevelType) => void;
}

// Almanca seviyeleri ve görsel gösterimleri
const LEVELS = [
  { value: 'all', label: 'Tüm Seviyeler', color: 'bg-gray-100 text-gray-600' },
  { value: 'a1', label: 'A1', color: 'bg-green-100 text-green-700' },
  { value: 'a2', label: 'A2', color: 'bg-green-200 text-green-800' },
  { value: 'b1', label: 'B1', color: 'bg-blue-100 text-blue-700' },
  { value: 'b2', label: 'B2', color: 'bg-blue-200 text-blue-800' },
  { value: 'c1', label: 'C1', color: 'bg-purple-100 text-purple-700' },
  { value: 'c2', label: 'C2', color: 'bg-purple-200 text-purple-800' }
];

export const PackageTypeFilter: React.FC<PackageTypeFilterProps> = ({ 
  packages, 
  activeFilter, 
  activeLevel, 
  onFilterChange, 
  onLevelChange 
}) => {
  // Paket tipleri için sayım yapacağız
  const countByType = {
    all: packages.length,
    birebir: packages.filter(p => p.type?.toLowerCase() === 'birebir' || p.type === '1').length,
    grup: packages.filter(p => p.type?.toLowerCase() === 'grup' || p.type === '2').length,
    video: packages.filter(p => p.type?.toLowerCase() === 'video' || p.type === '3').length,
  };

  return (
    <div className="w-full mb-6">
      {/* Paket Tipi Filtresi */}
      <div className="flex flex-wrap gap-2 mb-4">
        <Button 
          variant={activeFilter === 'all' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onFilterChange('all')}
          className="flex items-center gap-1.5"
        >
          {activeFilter === 'all' && <Check className="w-3.5 h-3.5" />}
          <span>Tümü</span>
          <span className="bg-gray-100 text-gray-700 text-xs rounded-full px-1.5 py-0.5 min-w-[1.5rem] text-center">
            {countByType.all}
          </span>
        </Button>
        
        {countByType.birebir > 0 && (
          <Button 
            variant={activeFilter === 'birebir' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onFilterChange('birebir')}
            className="flex items-center gap-1.5"
          >
            {activeFilter === 'birebir' ? (
              <Check className="w-3.5 h-3.5" />
            ) : (
              <Target className="w-3.5 h-3.5 text-blue-600" />
            )}
            <span>Birebir</span>
            <span className="bg-blue-100 text-blue-700 text-xs rounded-full px-1.5 py-0.5 min-w-[1.5rem] text-center">
              {countByType.birebir}
            </span>
          </Button>
        )}
        
        {countByType.grup > 0 && (
          <Button 
            variant={activeFilter === 'grup' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onFilterChange('grup')}
            className="flex items-center gap-1.5"
          >
            {activeFilter === 'grup' ? (
              <Check className="w-3.5 h-3.5" />
            ) : (
              <Users className="w-3.5 h-3.5 text-green-600" />
            )}
            <span>Grup</span>
            <span className="bg-green-100 text-green-700 text-xs rounded-full px-1.5 py-0.5 min-w-[1.5rem] text-center">
              {countByType.grup}
            </span>
          </Button>
        )}
        
        {countByType.video > 0 && (
          <Button 
            variant={activeFilter === 'video' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onFilterChange('video')}
            className="flex items-center gap-1.5"
          >
            {activeFilter === 'video' ? (
              <Check className="w-3.5 h-3.5" />
            ) : (
              <Video className="w-3.5 h-3.5 text-purple-600" />
            )}
            <span>Video</span>
            <span className="bg-purple-100 text-purple-700 text-xs rounded-full px-1.5 py-0.5 min-w-[1.5rem] text-center">
              {countByType.video}
            </span>
          </Button>
        )}
      </div>
      
      {/* Seviye Filtresi */}
      <div className="flex flex-wrap gap-2">
        {LEVELS.map(level => (
          <Button
            key={level.value}
            variant={activeLevel === level.value ? 'default' : 'outline'}
            size="sm"
            onClick={() => onLevelChange(level.value as LevelType)}
            className={`text-xs px-2 ${activeLevel === level.value ? '' : level.color}`}
          >
            {level.label}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default PackageTypeFilter;
