import React, { useState, useCallback } from 'react';
import { MessageCircle, Calendar, Copy, Check, PlayCircle, Video } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { VerifiedBadge } from '@/components/VerifiedBadge';
import { ProfilePhotoModal } from '@/components/modals/ProfilePhotoModal';
import { TeacherMessageModal } from '@/components/modals/TeacherMessageModal';
import { TeacherTrustBadges } from './TeacherTrustBadges';
import { SocialMediaLinks } from './SocialMediaLinks';
import { TeacherProfileClientData } from '@/types/teacher';

interface HeaderProps {
  teacher: TeacherProfileClientData;
  onShowCalendar: () => void;
  onShowTrialModal: () => void;
}

export const TeacherProfileHero: React.FC<HeaderProps> = ({ teacher, onShowCalendar, onShowTrialModal }) => {
  const [copied, setCopied] = useState(false);
  const [showPhotoModal, setShowPhotoModal] = useState(false);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);

  const handleShare = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Kopyalama başarısız:', err);
    }
  }, []);

  const shareToWhatsApp = useCallback(() => {
    const text = `${teacher.name} ile Almanca öğrenin! ${window.location.href}`;
    window.open(`https://wa.me/?text=${encodeURIComponent(text)}`, '_blank');
  }, [teacher.name]);

  const shareToX = useCallback(() => {
    const text = `${teacher.name} ile Almanca öğrenin!`;
    window.open(`https://x.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(window.location.href)}`, '_blank');
  }, [teacher.name]);

  const shareToFacebook = useCallback(() => {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`, '_blank');
  }, []);

  const handlePhotoClick = useCallback(() => {
    setShowPhotoModal(true);
  }, []);

  const handlePhotoModalClose = useCallback(() => {
    setShowPhotoModal(false);
  }, []);

  const handleVideoPlay = useCallback(() => {
    setShowVideoModal(true);
  }, []);

  const handleVideoModalClose = useCallback(() => {
    setShowVideoModal(false);
  }, []);

  const handleMessageClick = useCallback(() => {
    setShowMessageModal(true);
  }, []);

  const handleMessageModalClose = useCallback(() => {
    setShowMessageModal(false);
  }, []);

  return (
    <>
      <header className="bg-white/90 backdrop-blur-sm border-b border-white/20 sticky top-0 left-0 right-0 z-50 shadow-sm w-full overflow-x-hidden">
        <div className="w-full lg:max-w-7xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8 box-border relative">
          {/* Share Buttons - Top Right */}
          <div className="absolute top-4 right-4 sm:right-6 lg:right-8 z-20">
            <div className="flex gap-1 sm:gap-2">
              <Button
                onClick={handleShare}
                variant="outline"
                size="sm"
                className="bg-white/80 hover:bg-white border-gray-200 text-gray-700 hover:text-gray-900 h-8 w-8 sm:h-9 sm:w-9 p-0 backdrop-blur-sm transition-all duration-200"
                title="Linki kopyala"
              >
                {copied ? <Check className="w-3 h-3 sm:w-4 sm:h-4 text-green-600" /> : <Copy className="w-3 h-3 sm:w-4 sm:h-4" />}
              </Button>
              <Button
                onClick={shareToWhatsApp}
                variant="outline"
                size="sm"
                className="bg-white/80 hover:bg-green-50 border-gray-200 text-green-600 hover:text-green-700 h-8 w-8 sm:h-9 sm:w-9 p-0 backdrop-blur-sm transition-all duration-200"
                title="WhatsApp'ta paylaş"
              >
                <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.52 3.48A11.94 11.94 0 0012 0C5.373 0 0 5.373 0 12a11.94 11.94 0 001.64 6.04L0 24l5.96-1.56A11.94 11.94 0 0012 24c6.627 0 12-5.373 12-12 0-3.2-1.25-6.2-3.48-8.52zM12 21.5a9.5 9.5 0 01-4.8-1.3l-.34-.2-3.54.93.94-3.45-.22-.35A9.5 9.5 0 1121.5 12 9.5 9.5 0 0112 21.5z"/>
                  <path d="M17.5 14.5c-.3 0-1.7-.8-2-1-.3-.2-.5-.2-.7 0-.2.2-.8 1-.9 1.2-.1.2-.2.3-.5.1-.3-.2-1.3-.5-2.5-1.5-.9-.8-1.5-1.8-1.7-2-.2-.2 0-.3.1-.5.1-.1.2-.3.3-.5.1-.2.1-.3 0-.5-.1-.2-.7-1.7-1-2.3-.3-.6-.6-.5-.7-.5-.2 0-.4 0-.6 0-.2 0-.5.2-.7.5-.2.3-.7.7-.7 1.7 0 1 .7 2 1 2.3.3.3 2 3 4.8 4.3 2.7 1.3 2.7.9 3.2.8.5-.1 1.7-.7 1.9-1.3.2-.6.2-1.1.1-1.3-.1-.2-.3-.3-.6-.4z"/>
                </svg>
              </Button>
              <Button
                onClick={shareToX}
                variant="outline"
                size="sm"
                className="bg-white/80 hover:bg-gray-100 border-gray-200 text-gray-700 hover:text-gray-900 h-8 w-8 sm:h-9 sm:w-9 p-0 backdrop-blur-sm transition-all duration-200"
                title="X'te paylaş"
              >
                <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                </svg>
              </Button>
              <Button
                onClick={shareToFacebook}
                variant="outline"
                size="sm"
                className="bg-white/80 hover:bg-blue-50 border-gray-200 text-blue-700 hover:text-blue-800 h-8 w-8 sm:h-9 sm:w-9 p-0 backdrop-blur-sm transition-all duration-200"
                title="Facebook'ta paylaş"
              >
                <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.675 0h-21.35C.6 0 0 .6 0 1.325v21.351C0 23.4.6 24 1.325 24H12.82v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.894-4.788 4.659-4.788 1.325 0 2.466.099 2.797.143v3.24l-1.918.001c-1.504 0-1.796.715-1.796 1.763v2.312h3.59l-.467 3.622h-3.123V24h6.116C23.4 24 24 23.4 24 22.675V1.325C24 .6 23.4 0 22.675 0z"/>
                </svg>
              </Button>
            </div>
          </div>

          <div className="flex flex-col lg:flex-row items-center lg:items-start gap-6 sm:gap-8 lg:gap-10 w-full">
            {/* Avatar Section with Premium Design */}
            <div className="flex-shrink-0 relative self-center lg:self-start mt-20 sm:mt-16 lg:mt-0">
              {/* Floating Premium Badge */}
              <div className="absolute -top-2 -left-8 sm:-top-3 sm:-left-12 lg:-left-16 z-30">
                <div className="bg-gradient-to-r from-amber-400 to-orange-500 text-white px-2 py-1 sm:px-3 sm:py-1.5 lg:px-4 lg:py-2 rounded-full shadow-lg transform -rotate-12 text-xs sm:text-sm lg:text-base font-bold">
                  ⭐ Premium
                </div>
              </div>

              {/* Animated Gradient Background */}
              <div className="absolute -inset-3 sm:-inset-4 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 rounded-full blur-xl animate-pulse"></div>

              <div className="relative bg-white rounded-full p-1.5 sm:p-2 shadow-xl border border-white/30">
                <button
                  onClick={handlePhotoClick}
                  className="block rounded-full transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-500/30"
                  title="Profil fotoğrafını büyüt"
                >
                  <Avatar className="w-48 h-48 sm:w-56 sm:h-56 lg:w-64 lg:h-64 border-3 sm:border-4 border-gradient-to-br from-blue-500 to-purple-600 cursor-pointer">
                    <AvatarImage src={teacher.avatar} alt={`${teacher.name} - Almanca Öğretmeni`} />
                    <AvatarFallback className="text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-br from-blue-600 to-purple-600 text-white">
                      {teacher.name.split(' ').map((n: string) => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                </button>

                {/* Online Status Badge */}
                <div className="absolute bottom-2 right-2 sm:bottom-3 sm:right-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-white rounded-full shadow-lg flex items-center justify-center border-3 border-gray-300">
                    <div className={`w-4 h-4 sm:w-5 sm:h-5 rounded-full relative ${teacher.isOnline ? 'bg-green-500' : 'bg-gray-400'}`}>
                      {teacher.isOnline && <div className="absolute inset-0 rounded-full bg-green-400 animate-ping"></div>}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Content Section */}
            <div className="flex-1 min-w-0 flex flex-col items-center lg:items-start w-full">
              {/* Title and Badges */}
              <div className="text-center lg:text-left w-full mb-4 sm:mb-6">                <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 leading-tight mb-2 sm:mb-3 md:mb-4 flex items-center justify-center lg:justify-start flex-wrap">
                  <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                    {teacher.name}
                  </span>                  <VerifiedBadge 
                    isVerified={true} 
                    className="ml-2" 
                    size="lg" 
                    variant="simple"
                  />
                </h1>
                <div className="flex flex-wrap justify-center lg:justify-start gap-2 sm:gap-3">
                  <Badge className={`w-fit border-0 shadow-md text-sm transition-colors duration-200 ${teacher.isOnline ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'}`}>
                    <div className={`w-2 h-2 rounded-full mr-1 ${teacher.isOnline ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                    {teacher.isOnline ? 'Çevrimiçi' : 'Çevrimdışı'}
                  </Badge>
                    {/* Video rozeti eklendi */}
                  {teacher.intro_video_url && (
                    <Badge 
                      className="w-fit border-0 shadow-md text-sm transition-colors duration-200 bg-blue-100 text-blue-700 cursor-pointer hover:bg-blue-200"
                      onClick={handleVideoPlay}
                    >
                      <PlayCircle className="w-3 h-3 mr-1" />
                      Tanıtım Videosu
                    </Badge>
                  )}
                </div>
                
                {/* Güven Rozetleri */}
                <TeacherTrustBadges
                  teacher={teacher}
                  className="mt-3"
                />
                
                {/* Sosyal Medya Bağlantıları */}
                <div className="mt-4 flex justify-center lg:justify-start">
                  <SocialMediaLinks
                    teacher={teacher}
                    variant="icon"
                  />
                </div>
              </div>{/* Kısa açıklama - tanıtım metni */}
              {teacher.shortBio && (
                <div className="text-center lg:text-left mb-4 px-2 sm:px-0">
                  <p className="text-gray-700 text-sm md:text-base">{teacher.shortBio}</p>
                  
                  {/* Video varsa video izleme butonu */}
                  {teacher.intro_video_url && (
                    <Button
                      onClick={handleVideoPlay}
                      variant="outline" 
                      size="sm"
                      className="mt-2 flex items-center gap-2 text-blue-700 hover:text-blue-800 hover:bg-blue-50 border-blue-200"
                    >
                      <PlayCircle className="w-4 h-4" />
                      <span>Tanıtım Videosunu İzle</span>
                    </Button>
                  )}
                </div>
              )}

              {/* Deneme Dersi Vurgu Alanı - Ana Butonlardan Önce */}
              <div className="w-full mb-6 px-4 sm:px-0">
                <div className="bg-gradient-to-r from-yellow-50 via-orange-50 to-red-50 border-2 border-yellow-300 rounded-xl p-4 relative overflow-hidden shadow-lg">
                  {/* Animasyonlu arka plan elementi */}
                  <div className="absolute top-0 right-0 w-24 h-24 bg-yellow-200/30 rounded-full -translate-y-12 translate-x-12 animate-pulse"></div>
                  <div className="absolute bottom-0 left-0 w-16 h-16 bg-orange-200/30 rounded-full translate-y-8 -translate-x-8 animate-pulse delay-1000"></div>
                  
                  <div className="relative z-10 text-center">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-bold tracking-wide">
                        ÜCRETSİZ DENEME DERSİ
                      </span>
                    </div>
                    
                    <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-1">
                      15 Dakika Ücretsiz Tanışma!
                    </h3>
                    <p className="text-gray-700 text-sm sm:text-base mb-3 leading-relaxed">
                      Hiçbir ödeme yapmadan seviyenizi belirleyin ve öğretmeninizle tanışın
                    </p>
                      <Button 
                      onClick={() => {
                        // Deneme dersi paketini seç ve trial modal aç
                        onShowTrialModal();
                      }}
                      className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-bold px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                    >
                      <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                      </svg>
                      Deneme Dersini Rezerve Et
                    </Button>
                  </div>
                </div>
              </div>

              {/* Main Action Buttons - Mobil uyumlu ve ekrana tam sığacak şekilde */}
              <div className="flex flex-col sm:flex-row gap-3 w-full px-4 sm:px-0">                <Button 
                  onClick={handleMessageClick}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] px-4 sm:px-6 py-3 text-base font-semibold"
                >
                  <MessageCircle className="w-5 h-5 mr-2 flex-shrink-0" />
                  <span className="truncate">Mesaj Gönder</span>
                </Button>
                <Button
                  onClick={onShowCalendar}
                  variant="secondary"
                  className="w-full bg-gray-900 hover:bg-gray-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] px-4 sm:px-6 py-3 text-base font-semibold"
                >
                  <Calendar className="w-5 h-5 mr-2 flex-shrink-0" />
                  <span className="truncate">Randevu Al</span>
                </Button>
              </div>

              {/* Video Introduction Section - Yeni eklenen bölüm */}
              <div className="mt-8 w-full">
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 text-center">
                  Almanca Öğretmeninizle Tanışın
                </h2>
                <div className="flex justify-center">
                  <Button
                    onClick={handleVideoPlay}
                    variant="outline"
                    className="bg-white/80 hover:bg-white border-gray-200 text-gray-700 hover:text-gray-900 rounded-full shadow-md px-6 py-3 flex items-center gap-2 transition-all duration-200"
                    title="Tanıtım videosunu oynat"
                  >
                    <PlayCircle className="w-5 h-5" />
                    <span className="text-sm sm:text-base font-semibold">Tanıtım Videosunu İzleyin</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>      {/* Profile Photo Modal */}
      <ProfilePhotoModal 
        isOpen={showPhotoModal}
        onClose={handlePhotoModalClose}
        teacher={teacher}
      />

      {/* Video Modal */}
      <VideoModal
        isOpen={showVideoModal}
        onClose={handleVideoModalClose}
        videoUrl={teacher.intro_video_url}
        teacherName={teacher.name}
      />

      {/* Mesaj Modalı */}
      <TeacherMessageModal
        isOpen={showMessageModal}
        onClose={handleMessageModalClose}
        teacher={teacher}
      />
    </>
  );
};

// Tanıtım videosu için basit modal bileşeni
interface VideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoUrl: string | undefined;
  teacherName: string;
}

const VideoModal: React.FC<VideoModalProps> = ({ isOpen, onClose, videoUrl, teacherName }) => {
  if (!isOpen) return null;
  
  // YouTube video ID'sini çıkart
  const getYouTubeEmbedUrl = (url: string | undefined) => {
    if (!url) return null;
    try {
      const urlObj = new URL(url);
      // youtube.com/watch?v=VIDEO_ID formatı
      const videoId = urlObj.searchParams.get('v');
      if (videoId) {
        return `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&showinfo=0`;
      }
      // youtu.be/VIDEO_ID formatı
      if (urlObj.hostname === 'youtu.be') {
        return `https://www.youtube.com/embed/${urlObj.pathname.slice(1)}?autoplay=1&rel=0&showinfo=0`;
      }
      // Doğrudan embed formatı
      if (url.includes('youtube.com/embed/')) {
        return url;
      }
    } catch {
      console.error("Geçersiz YouTube URL'i:", url);
      return null;
    }
    return null;
  };

  const embedUrl = getYouTubeEmbedUrl(videoUrl);

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="relative bg-white rounded-xl shadow-2xl w-full max-w-4xl">
        <button 
          onClick={onClose}
          className="absolute -top-12 right-0 text-white hover:text-gray-200 p-2 z-10"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        <div className="p-4">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <Video className="w-5 h-5 text-blue-600" />
            {teacherName} - Tanıtım Videosu
          </h3>
          
          <div className="relative pb-[56.25%] h-0 overflow-hidden rounded-lg">
            {embedUrl ? (
              <iframe 
                src={embedUrl}
                title={`${teacherName} - Tanıtım Videosu`}
                className="absolute top-0 left-0 w-full h-full border-0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              ></iframe>
            ) : (
              <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-gray-100">
                <p className="text-gray-500">Video yüklenemedi.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
