// src/app/ogretmenler/[id]/page.tsx
export const revalidate = 3600; // 1 saat

import React from 'react';
import { notFound } from 'next/navigation';
import { getTeacherProfileData } from "@/lib/actions/teacher.actions";
import TeacherProfileClient from '@/components/teacher/TeacherProfileClient';
import { Prisma } from '@prisma/client';

// Schema.org JSON-LD structured data
function generateTeacherSchema(teacher: any) { // eslint-disable-line @typescript-eslint/no-explicit-any
  const teacherName = teacher.name || `${teacher.firstName} ${teacher.lastName}`;
  
  return {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": teacherName,
    "jobTitle": teacher.title,
    "description": teacher.shortBio || teacher.bio,
    "image": teacher.avatar,
    "url": `https://almancaabc.com/ogretmenler/${teacher.id}`,
    "email": teacher.email,
    "address": teacher.country || teacher.city,
    "nationality": teacher.country,
    "knowsLanguage": teacher.languages,
    "aggregateRating": teacher.average_rating ? {
      "@type": "AggregateRating",
      "ratingValue": teacher.average_rating,
      "reviewCount": teacher.reviewCount || 0,
      "bestRating": 5,
      "worstRating": 1
    } : undefined,
    "offers": teacher.lessonPackages?.map((pkg: any) => ({ // eslint-disable-line @typescript-eslint/no-explicit-any
      "@type": "Offer",
      "name": pkg.name,
      "description": pkg.description,
      "price": pkg.price,
      "priceCurrency": "TRY",
      "availability": "https://schema.org/InStock"
    })),
    "review": teacher.reviewsReceived?.map((review: any) => ({ // eslint-disable-line @typescript-eslint/no-explicit-any
      "@type": "Review",
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": review.rating,
        "bestRating": 5,
        "worstRating": 1
      },
      "reviewBody": review.comment,
      "author": {
        "@type": "Person",
        "name": `${review.student.firstName || ''} ${review.student.lastName || ''}`.trim() || 'Bir Öğrenci'
      }
    }))
  };
}


import { Metadata } from 'next';

interface TeacherProfilePageProps {
  params: Promise<{ id: string }>;
}

export async function generateMetadata(
  { params }: TeacherProfilePageProps
): Promise<Metadata> {
  const { id } = await params;
  const teacher = await getTeacherProfileData(id);

  if (!teacher) {
    return {
      title: "Öğretmen Bulunamadı | AlmancaABC",
      description: "Aradığınız öğretmen profili bulunamadı veya henüz onaylanmamış.",
    };
  }
  
  const teacherName = teacher.name || `${teacher.firstName} ${teacher.lastName}`;
  const description = teacher.shortBio || `AlmancaABC platformunda ${teacherName} ile online Almanca öğrenin.`;
  const imageUrl = teacher.avatar || '/placeholder.svg';

  return {
    title: `${teacherName} - Online Almanca Öğretmeni | AlmancaABC`,
    description: description,
    openGraph: {
      title: `${teacherName} - Online Almanca Öğretmeni`,
      description: description,
      images: [imageUrl],
      type: 'profile',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${teacherName} - Online Almanca Öğretmeni`,
      description: description,
      images: [imageUrl],
    },
  };
}

const TeacherProfilePage = async ({ params }: TeacherProfilePageProps) => {
  const { id } = await params;
  const teacherData = await getTeacherProfileData(id);

  if (!teacherData) {
    notFound();
  }

  // Get some recommended teachers (simple implementation for now)
  // In a real app, this would be based on similar specializations, ratings, etc.
  const recommendedTeachers: any[] = []; // eslint-disable-line @typescript-eslint/no-explicit-any
  // TODO: Implement proper recommendation logic
  // TeacherProfileClient'in beklediği prop'ları hazırlayalım.
  // getTeacherProfileData'dan gelen veriyi TeacherProfileClientData tipine dönüştürelim.
  const teacherForClient = {
    id: teacherData.id,
    name: teacherData.name || `${teacherData.firstName} ${teacherData.lastName}`,
    firstName: teacherData.firstName,
    lastName: teacherData.lastName,
    email: teacherData.email || undefined,
    avatar: teacherData.avatar || undefined,
    bio: teacherData.bio || undefined,
    title: teacherData.title || undefined,
    shortBio: teacherData.shortBio || '',
    specializations: teacherData.specializations,
    levels: teacherData.levels,
    hourly_rate: teacherData.hourly_rate,
    intro_video_url: teacherData.intro_video_url || undefined,
    country: teacherData.country || undefined,
    city: teacherData.city || undefined,
    nativeLanguage: teacherData.nativeLanguage || undefined,
    languages: teacherData.languages,
    isVerified: teacherData.isVerified,
    isOnline: true, // Şimdilik sabit olarak true, daha sonra gerçek veri ile değiştirilecek
    experienceYears: teacherData.experienceYears?.toString() || undefined,
    average_rating: teacherData.average_rating,
    totalReviews: teacherData.reviewCount,
    activeStudentCount: teacherData.activeStudentCount || 0,
    completedLessons: teacherData.totalLessons,
    timezone: teacherData.timezone,
    // Teacher tipindeki ek alanlar
    rating: teacherData.average_rating || 0,
    reviewCount: teacherData.reviewCount || teacherData.totalReviews || 0,
    price: teacherData.hourly_rate || 0,
    badges: teacherData.isVerified ? ['Doğrulanmış'] : [],
    is_verified: teacherData.isVerified,
    activeStudents: teacherData.activeStudentCount || 0,
    totalLessons: teacherData.totalLessons || teacherData.completedLessons || 0,
    isSuperTeacher: teacherData.isVerified || false,
    spokenLanguages: teacherData.languages.map(lang => ({
      language: lang,
      level: 'native'
    })),
    created_at: teacherData.created_at.toISOString(),
    updated_at: teacherData.updated_at.toISOString(),
    education: teacherData.education,
    certificates: teacherData.certificates,
    reviews: teacherData.reviewsReceived.map(review => ({
      id: review.id,
      rating: review.rating,
      comment: review.comment || undefined,
      date: review.createdAt.toISOString(),
      student: {
        id: review.student.id,
        name: `${review.student.firstName || ''} ${review.student.lastName || ''}`.trim() || 'Bir Öğrenci',
        avatar: review.student.profile_image_url || null,
      },
    })),    lessonPackages: teacherData.lessonPackages.map(pkg => {
      // Tüm alanları açık bir şekilde tanımlayalım
      return {
        id: pkg.id,
        name: pkg.name,
        description: pkg.description || undefined,
        level: pkg.level || undefined,
        thumbnailUrl: pkg.thumbnailUrl || undefined,
        createdAt: pkg.createdAt.toISOString(),
        updatedAt: pkg.updatedAt.toISOString(),
        price: new Prisma.Decimal(pkg.price).toNumber(),
        pricePerLessonCalculated: pkg.pricePerLessonCalculated ? new Prisma.Decimal(pkg.pricePerLessonCalculated).toNumber() : null,
        lessonsInPackage: pkg.lessonsInPackage,
        lessonDurationMinutes: pkg.lessonDurationMinutes,
        discountPercentage: pkg.discountPercentage,
        features: pkg.features || [],
        isActive: pkg.isActive,
        // Opsiyonel alanlar
        shortDesc: pkg.description?.substring(0, 80) || '',
        type: pkg.type || 'birebir',
        options: [],
        free: false,
        duration: undefined,
        // Diğer özel alanlar
        groupCourseStartDate: pkg.groupCourseStartDate ? pkg.groupCourseStartDate.toISOString() : null,
        enrolledStudentCount: pkg.enrolledStudentCount || null,
        studentCapacity: pkg.studentCapacity || null,
        groupCourseSchedule: pkg.groupCourseSchedule || null,
        videoCourseTotalLessons: pkg.videoCourseTotalLessons || null,
        videoCourseTotalHours: pkg.videoCourseTotalHours || null,
        youtubePlaylistUrl: pkg.youtubePlaylistUrl || null,
        videoCourseIsPopular: pkg.videoCourseIsPopular || null,
        videoCourseMaterials: pkg.videoCourseMaterials || null,
      };
    }),
    oneOnOnePackages: teacherData.oneOnOnePackages.map(pkg => ({
      id: pkg.id,
      name: pkg.name,
      description: pkg.description || undefined,
      level: pkg.level || undefined,
      thumbnailUrl: pkg.thumbnailUrl || undefined,
      createdAt: pkg.createdAt.toISOString(),
      updatedAt: pkg.updatedAt.toISOString(),
      price: new Prisma.Decimal(pkg.price).toNumber(),
      pricePerLessonCalculated: pkg.pricePerLessonCalculated ? new Prisma.Decimal(pkg.pricePerLessonCalculated).toNumber() : null,
      lessonsInPackage: pkg.lessonsInPackage,
      lessonDurationMinutes: pkg.lessonDurationMinutes,
      discountPercentage: pkg.discountPercentage,
      features: pkg.features || [],
      isActive: pkg.isActive,
      // Opsiyonel alanlar
      shortDesc: pkg.description?.substring(0, 80) || '',
      type: pkg.type || 'birebir',
      options: [],
      free: false,
      duration: undefined,
      // Diğer özel alanlar
      groupCourseStartDate: pkg.groupCourseStartDate ? pkg.groupCourseStartDate.toISOString() : null,
      enrolledStudentCount: pkg.enrolledStudentCount || null, 
      studentCapacity: pkg.studentCapacity || null,
      groupCourseSchedule: pkg.groupCourseSchedule || null,
      videoCourseTotalLessons: pkg.videoCourseTotalLessons || null,
      videoCourseTotalHours: pkg.videoCourseTotalHours || null,
      youtubePlaylistUrl: pkg.youtubePlaylistUrl || null,
      videoCourseIsPopular: pkg.videoCourseIsPopular || null,
      videoCourseMaterials: pkg.videoCourseMaterials || null,
    })),
    groupCourses: teacherData.groupCourses.map(pkg => ({
      id: pkg.id,
      name: pkg.name,
      description: pkg.description || undefined,
      level: pkg.level || undefined,
      thumbnailUrl: pkg.thumbnailUrl || undefined,
      createdAt: pkg.createdAt.toISOString(),
      updatedAt: pkg.updatedAt.toISOString(),
      price: new Prisma.Decimal(pkg.price).toNumber(),
      pricePerLessonCalculated: pkg.pricePerLessonCalculated ? new Prisma.Decimal(pkg.pricePerLessonCalculated).toNumber() : null,
      lessonsInPackage: pkg.lessonsInPackage,
      lessonDurationMinutes: pkg.lessonDurationMinutes,
      discountPercentage: pkg.discountPercentage,
      features: pkg.features || [],
      isActive: pkg.isActive,
      // Opsiyonel alanlar
      shortDesc: pkg.description?.substring(0, 80) || '',
      type: pkg.type || 'grup',
      options: [],
      free: false,
      duration: undefined,
      // Diğer özel alanlar
      groupCourseStartDate: pkg.groupCourseStartDate ? pkg.groupCourseStartDate.toISOString() : null,
      enrolledStudentCount: pkg.enrolledStudentCount || null,
      studentCapacity: pkg.studentCapacity || null,
      groupCourseSchedule: pkg.groupCourseSchedule || null,
      videoCourseTotalLessons: pkg.videoCourseTotalLessons || null,
      videoCourseTotalHours: pkg.videoCourseTotalHours || null,
      youtubePlaylistUrl: pkg.youtubePlaylistUrl || null,
      videoCourseIsPopular: pkg.videoCourseIsPopular || null,
      videoCourseMaterials: pkg.videoCourseMaterials || null,
    })),
    videoCourses: teacherData.videoCourses.map(pkg => ({
      id: pkg.id,
      name: pkg.name,
      description: pkg.description || undefined,
      level: pkg.level || undefined,
      thumbnailUrl: pkg.thumbnailUrl || undefined,
      createdAt: pkg.createdAt.toISOString(),
      updatedAt: pkg.updatedAt.toISOString(),
      price: new Prisma.Decimal(pkg.price).toNumber(),
      pricePerLessonCalculated: pkg.pricePerLessonCalculated ? new Prisma.Decimal(pkg.pricePerLessonCalculated).toNumber() : null,
      lessonsInPackage: pkg.lessonsInPackage,
      lessonDurationMinutes: pkg.lessonDurationMinutes,
      discountPercentage: pkg.discountPercentage,
      features: pkg.features || [],
      isActive: pkg.isActive,
      // Opsiyonel alanlar
      shortDesc: pkg.description?.substring(0, 80) || '',
      type: pkg.type || 'video',
      options: [],
      free: pkg.videoCourseIsPopular || false, // Tanıtım için bedava olabilir
      duration: pkg.videoCourseTotalHours ? `${pkg.videoCourseTotalHours} saat` : undefined,
      // Diğer özel alanlar
      groupCourseStartDate: pkg.groupCourseStartDate ? pkg.groupCourseStartDate.toISOString() : null,
      enrolledStudentCount: pkg.enrolledStudentCount || null,
      studentCapacity: pkg.studentCapacity || null,
      groupCourseSchedule: pkg.groupCourseSchedule || null,
      videoCourseTotalLessons: pkg.videoCourseTotalLessons || null,
      videoCourseTotalHours: pkg.videoCourseTotalHours || null,
      youtubePlaylistUrl: pkg.youtubePlaylistUrl || null, 
      videoCourseIsPopular: pkg.videoCourseIsPopular || null,
      videoCourseMaterials: pkg.videoCourseMaterials || null,
    })),
    teacherFaqs: teacherData.teacherFaqs || [],
  };

  return (
    <div className="min-h-screen bg-gray-50" suppressHydrationWarning>
      <React.Suspense fallback={<div>Yükleniyor...</div>}>
        <TeacherProfileClient teacher={teacherForClient} recommendedTeachers={recommendedTeachers} />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(generateTeacherSchema(teacherData)) }}
        />
      </React.Suspense>
    </div>
  );
}

export default TeacherProfilePage;
