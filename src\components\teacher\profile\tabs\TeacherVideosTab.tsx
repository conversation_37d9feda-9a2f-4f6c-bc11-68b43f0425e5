import React from 'react';
import Image from 'next/image';
import { Video, PlayCircle, Lock, Info } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { TeacherProfileClientData } from '@/types/teacher';

interface TeacherVideosTabProps {
  teacher: TeacherProfileClientData;
  onVideoPlay: (videoIndex: number) => void;
}

export const TeacherVideosTab: React.FC<TeacherVideosTabProps> = ({ teacher, onVideoPlay }) => {
  // Eğer tanıtım videosu varsa, en başa ekle
  const hasIntroVideo = !!teacher.intro_video_url;
  
  return (
    <div className="space-y-8 p-4 md:p-6">
      {/* Öğretmen tanıtım videosu bölümü */}
      {hasIntroVideo && (
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 shadow-md overflow-hidden">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <Video className="w-6 h-6 text-blue-600" />
              Öğretmen Tanıtım Videosu
            </CardTitle>
            <CardDescription>
              {teacher.name} ile tanışın ve öğretim tarzını keşfedin
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col lg:flex-row gap-6">
              <div className="flex-1 lg:w-2/3">
                <div 
                  className="relative overflow-hidden rounded-xl cursor-pointer group"
                  onClick={() => onVideoPlay(0)}
                >
                  <div className="relative aspect-video bg-gray-100 rounded-xl overflow-hidden shadow-md">
                    <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-r from-blue-900/30 to-indigo-900/30 group-hover:from-blue-900/10 group-hover:to-indigo-900/10 transition-all duration-300">
                      <div className="w-20 h-20 rounded-full bg-white/80 group-hover:bg-white/95 flex items-center justify-center transition-all duration-300 group-hover:scale-110">
                        <PlayCircle className="w-12 h-12 text-blue-600" />
                      </div>
                    </div>                    <Image
                      src={getYoutubeThumbnail(teacher.intro_video_url) || '/placeholder.svg'}
                      alt={`${teacher.name} - Tanıtım Videosu`}
                      width={640}
                      height={360}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder.svg';
                      }}
                    />
                  </div>
                </div>
              </div>
              
              <div className="flex-1 lg:w-1/3 flex flex-col justify-center">
                <h3 className="text-xl font-medium text-gray-900 mb-2">Merhaba, ben {teacher.name}!</h3>
                <p className="text-gray-700 mb-4 line-clamp-3">
                  {teacher.shortBio || 'Almanca öğretimine dair yaklaşımımı ve ders metodolojimi bu videoda bulabilirsiniz.'}
                </p>
                
                <div className="flex gap-2">
                  <Button
                    onClick={() => onVideoPlay(0)}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <PlayCircle className="w-4 h-4 mr-2" />
                    Tanıtım Videosunu İzle
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Video kurslar bölümü */}
      <Card className="bg-white border border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-gray-800">
            <Video className="w-6 h-6 text-blue-500" />
            Video Dersler
          </CardTitle>
        </CardHeader>
        <CardContent>
          {teacher.videoCourses && teacher.videoCourses.length > 0 ? (
            <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-5 lg:gap-6">
              {teacher.videoCourses.map((video, index) => (
                <div
                  key={video.id || index}
                  className="group cursor-pointer transform hover:scale-105 transition-all duration-200 bg-white rounded-lg shadow-sm hover:shadow-md border border-gray-200 overflow-hidden"
                  onClick={() => onVideoPlay(index + (hasIntroVideo ? 1 : 0))}
                >
                  <div className="relative overflow-hidden bg-gray-100">                    <Image
                      src={video.thumbnailUrl || '/placeholder.svg'}
                      alt={video.name || 'Video ders'}
                      width={320}
                      height={180}
                      className="w-full h-40 object-cover group-hover:scale-110 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder.svg';
                      }}
                    />
                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      {video.free ? (
                        <div className="bg-white/90 rounded-full p-3 group-hover:scale-110 transition-transform duration-300">
                          <PlayCircle className="w-8 h-8 text-blue-600" />
                        </div>
                      ) : (
                        <div className="bg-white/90 rounded-full p-3">
                          <Lock className="w-8 h-8 text-gray-600" />
                        </div>
                      )}
                    </div>
                    {video.duration && (
                      <Badge className="absolute bottom-2 right-2 bg-black/70 text-white">
                        {video.duration}
                      </Badge>
                    )}
                    {video.level && (
                      <Badge 
                        className={`absolute top-2 left-2 ${
                          video.level === 'Temel' 
                            ? 'bg-green-500' 
                            : video.level === 'Orta' 
                            ? 'bg-yellow-500' 
                            : 'bg-red-500'
                        } text-white`}
                      >
                        {video.level}
                      </Badge>
                    )}
                    {video.free && (
                      <Badge className="absolute top-2 right-2 bg-blue-500 text-white">
                        Ücretsiz
                      </Badge>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium text-lg text-gray-900 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2 mb-1">
                      {video.name || 'Video Başlığı'}
                    </h3>
                    <p className="text-gray-600 text-sm line-clamp-2">
                      {video.description || 'Bu video dersi hakkında detaylı açıklama.'}
                    </p>
                    <div className="flex justify-between items-center mt-3">
                      <div className="text-blue-600 font-medium">
                        {video.price ? `${video.price} ₺` : 'Ücretsiz'}
                      </div>
                      <Button size="sm" variant="secondary" className="text-xs">
                        <PlayCircle className="w-3 h-3 mr-1" />
                        İzle
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Bilgi</AlertTitle>
              <AlertDescription>
                Bu öğretmen için henüz video ders bulunmamaktadır.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// YouTube thumbnail URL'i oluşturan yardımcı fonksiyon
function getYoutubeThumbnail(url: string | undefined): string | null {
  if (!url) return null;
  
  try {
    // youtube.com/watch?v=VIDEO_ID formatı
    const regex1 = /(?:youtube\.com\/watch\?v=|youtu.be\/|youtube.com\/embed\/)([^&?/]+)/;
    const match = url.match(regex1);
    
    if (match && match[1]) {
      // Yüksek kaliteli thumbnail
      return `https://img.youtube.com/vi/${match[1]}/hqdefault.jpg`;
    }
  } catch (error) {
    console.error("YouTube thumbnail alınırken hata:", error);
  }
  
  return null;
}
