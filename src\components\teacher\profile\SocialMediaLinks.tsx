import React from 'react';
import { Instagram, Linkedin, Globe, Youtube, Facebook, Twitter } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { TeacherProfileClientData } from '@/types/teacher';

interface SocialMediaLinksProps {
  teacher: TeacherProfileClientData;
  className?: string;
  variant?: 'default' | 'button' | 'icon';
}

export const SocialMediaLinks: React.FC<SocialMediaLinksProps> = ({ 
  teacher,
  className,
  variant = 'default'
}) => {
  // Bu örnek için sabit sosyal medya bağlantılarını tanımlıyoruz
  // Normalde öğretmen objesinden gelmesi gerekir
  const socialLinks = [
    {
      id: 'website',
      url: teacher.website || 'https://www.almancaabc.com/ogretmenler/' + teacher.id,
      icon: Globe,
      label: 'Web Sitesi',
      color: 'text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 border-gray-200'
    },
    {
      id: 'instagram',
      url: teacher.instagramUrl || `https://www.instagram.com/${teacher.name?.toLowerCase().replace(/\s+/g, '')}`,
      icon: Instagram,
      label: 'Instagram',
      color: 'text-pink-600 hover:text-pink-700 bg-pink-50 hover:bg-pink-100 border-pink-100'
    },
    {
      id: 'linkedin',
      url: teacher.linkedinUrl || `https://www.linkedin.com/in/${teacher.name?.toLowerCase().replace(/\s+/g, '-')}`,
      icon: Linkedin,
      label: 'LinkedIn',
      color: 'text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 border-blue-100'
    },
    {
      id: 'youtube',
      url: teacher.youtubeUrl || `https://www.youtube.com/@${teacher.name?.toLowerCase().replace(/\s+/g, '')}`,
      icon: Youtube,
      label: 'YouTube',
      color: 'text-red-600 hover:text-red-700 bg-red-50 hover:bg-red-100 border-red-100'
    },
    {
      id: 'facebook',
      url: teacher.facebookUrl || `https://www.facebook.com/${teacher.name?.toLowerCase().replace(/\s+/g, '')}`,
      icon: Facebook,
      label: 'Facebook',
      color: 'text-blue-700 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 border-blue-100'
    },
    {
      id: 'twitter',
      url: teacher.xUrl || `https://x.com/${teacher.name?.toLowerCase().replace(/\s+/g, '')}`,
      icon: Twitter,
      label: 'X / Twitter',
      color: 'text-gray-700 hover:text-gray-900 bg-gray-50 hover:bg-gray-100 border-gray-100'
    }
  ];

  const renderDefault = () => (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {socialLinks.map((link) => (
        <TooltipProvider key={link.id} delayDuration={200}>
          <Tooltip>
            <TooltipTrigger asChild>
              <a 
                href={link.url} 
                target="_blank" 
                rel="noopener noreferrer"
                className={cn(
                  "p-2 rounded-full flex items-center justify-center transition-all duration-200",
                  link.color
                )}
                aria-label={link.label}
              >
                <link.icon className="w-4 h-4" />
              </a>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>{link.label}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ))}
    </div>
  );

  const renderButtons = () => (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {socialLinks.map((link) => (
        <Button
          key={link.id}
          size="sm"
          variant="outline"
          className={cn(
            "rounded-md flex items-center gap-1.5", 
            link.color
          )}
          asChild
        >
          <a 
            href={link.url} 
            target="_blank" 
            rel="noopener noreferrer"
            aria-label={link.label}
          >
            <link.icon className="w-3.5 h-3.5" />
            <span>{link.label}</span>
          </a>
        </Button>
      ))}
    </div>
  );

  const renderIcons = () => (
    <div className={cn("flex flex-wrap gap-3", className)}>
      {socialLinks.map((link) => (
        <a 
          key={link.id}
          href={link.url} 
          target="_blank" 
          rel="noopener noreferrer"
          className="transition-transform duration-200 hover:scale-110"
          aria-label={link.label}
        >
          <link.icon className={cn("w-5 h-5", link.id === 'instagram' ? 'text-pink-500' : link.id === 'linkedin' ? 'text-blue-600' : link.id === 'youtube' ? 'text-red-600' : link.id === 'facebook' ? 'text-blue-700' : link.id === 'twitter' ? 'text-black' : 'text-gray-600')} />
        </a>
      ))}
    </div>
  );

  if (variant === 'button') {
    return renderButtons();
  }

  if (variant === 'icon') {
    return renderIcons();
  }

  return renderDefault();
};
