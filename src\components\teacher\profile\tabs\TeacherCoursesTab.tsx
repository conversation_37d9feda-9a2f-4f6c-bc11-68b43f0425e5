import React, { useState } from 'react';
import { CreditCard, CheckCircle, Clock, Star, Zap, Target, Users, Video, TrendingUp, ChevronDown, ChevronUp, Activity, Eye, CalendarClock, Calendar, SearchIcon } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TeacherProfileClientData, ClientPackage as Package, PackageOption } from '@/types/teacher';
import PackageCompareModal from "@/components/modals/PackageCompareModal";
import PackageTypeFilter from "@/components/teacher/profile/PackageTypeFilter";
import PackageQuickView from "@/components/modals/PackageQuickView";
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';

interface TeacherCoursesTabProps {
  teacher: TeacherProfileClientData;
  onPackageSelect: (pkg: Package, option: PackageOption) => void;
}

type FilterType = 'all' | 'birebir' | 'grup' | 'video';
type LevelType = 'all' | 'a1' | 'a2' | 'b1' | 'b2' | 'c1' | 'c2';

export const TeacherCoursesTab: React.FC<TeacherCoursesTabProps> = ({ teacher, onPackageSelect }) => {
  const [activeFilter, setActiveFilter] = useState<FilterType>('all');
  const [activeLevel, setActiveLevel] = useState<LevelType>('all');
  const [packagesToCompare, setPackagesToCompare] = useState<Package[]>([]);
  const [showCompareModal, setShowCompareModal] = useState(false);
  const [expandedPackageIds, setExpandedPackageIds] = useState<(string | number)[]>([]);
  const [quickViewPackage, setQuickViewPackage] = useState<Package | null>(null);
  const [quickViewIsOpen, setQuickViewIsOpen] = useState(false);

  const packageIcons: { [key: string]: React.ElementType } = {
    '1': Target,
    'birebir': Target,
    '2': Users,
    'grup': Users,
    '3': Video,
    'video': Video
  };

  const packageColors: { [key: string]: { gradient: string, bg: string, border: string } } = {
    '1': { 
      gradient: "from-blue-500 to-blue-600", 
      bg: "bg-blue-500", 
      border: "border-blue-200" 
    },
    'birebir': { 
      gradient: "from-blue-500 to-blue-600", 
      bg: "bg-blue-500", 
      border: "border-blue-200" 
    },
    '2': { 
      gradient: "from-green-500 to-green-600", 
      bg: "bg-green-500", 
      border: "border-green-200" 
    },
    'grup': { 
      gradient: "from-green-500 to-green-600", 
      bg: "bg-green-500", 
      border: "border-green-200" 
    },
    '3': { 
      gradient: "from-purple-500 to-purple-600", 
      bg: "bg-purple-500", 
      border: "border-purple-200" 
    },
    'video': { 
      gradient: "from-purple-500 to-purple-600", 
      bg: "bg-purple-500", 
      border: "border-purple-200" 
    }
  };

  // Tüm paketleri birleştir
  const allPackages = [
    ...(teacher.oneOnOnePackages || []),
    ...(teacher.groupCourses || []),
    ...(teacher.videoCourses || [])
  ];
  
  // Filtreleme mantığı
  const filteredPackages = allPackages.filter(pkg => {
    // Paket tipine göre filtrele
    if (activeFilter !== 'all') {
      const pkgType = pkg.type?.toLowerCase() || '';
      if (activeFilter === 'birebir' && pkgType !== 'birebir' && pkgType !== '1') return false;
      if (activeFilter === 'grup' && pkgType !== 'grup' && pkgType !== '2') return false;
      if (activeFilter === 'video' && pkgType !== 'video' && pkgType !== '3') return false;
    }
    
    // Seviyeye göre filtrele
    if (activeLevel !== 'all') {
      const pkgLevel = pkg.level?.toLowerCase() || '';
      if (!pkgLevel.includes(activeLevel)) return false;
    }
    
    return true;
  });
  
  // Paket karşılaştırma fonksiyonları
  const togglePackageComparison = (pkg: Package) => {
    setPackagesToCompare(prev => {
      const isSelected = prev.some(p => p.id === pkg.id);
      if (isSelected) {
        return prev.filter(p => p.id !== pkg.id);
      } else {
        if (prev.length >= 3) {
          // Maksimum 3 paket seçilebilir
          return [...prev.slice(1), pkg];
        }
        return [...prev, pkg];
      }
    });
  };
  
  const openCompareModal = () => {
    if (packagesToCompare.length >= 2) {
      setShowCompareModal(true);
    }
  };
  
  const closeCompareModal = () => {
    setShowCompareModal(false);
  };
  
  // Detay açma/kapama fonksiyonu
  const togglePackageDetails = (packageId: string | number) => {
    setExpandedPackageIds(prev => {
      if (prev.includes(packageId)) {
        return prev.filter(id => id !== packageId);
      } else {
        return [...prev, packageId];
      }
    });
  };
  
  // Hızlı bakış fonksiyonları
  const openQuickView = (pkg: Package) => {
    setQuickViewPackage(pkg);
    setQuickViewIsOpen(true);
  };
  
  const closeQuickView = () => {
    setQuickViewIsOpen(false);
    setQuickViewPackage(null);
  };
  
  // Görsel başlık - Farklı filtrelere göre başlık ve açıklama
  const getFilterTitle = () => {
    switch(activeFilter) {
      case 'birebir':
        return "Birebir Dersler";
      case 'grup':
        return "Grup Dersleri";
      case 'video':
        return "Video Kursları";
      default:
        return "Tüm Kurslar ve Paketler";
    }
  };
  
  const getFilterDescription = () => {
    switch(activeFilter) {
      case 'birebir':
        return "Kişiselleştirilmiş birebir derslerle hızlı ve etkili öğrenin. Öğretmeniniz ile birebir çalışarak hedeflerinize ulaşın.";
      case 'grup':
        return "Diğer öğrencilerle birlikte öğrenmenin avantajını yaşayın. Ekonomik fiyatlarla etkili dil eğitimi grup dersleri.";
      case 'video':
        return "Zaman ve mekandan bağımsız öğrenme fırsatı. İstediğiniz zaman izleyebileceğiniz video kursları.";
      default:
        return "Almanca öğrenme hedeflerinize uygun kurs seçeneklerini keşfedin. Her seviye için özel olarak tasarlanmış programlarımızla hızlı ve etkili öğrenin.";
    }
  };
    // Grup kursları için doluluk oranı hesaplama
  const calculateFilledPercentage = (pkg: Package) => {
    if (pkg.enrolledStudentCount && pkg.studentCapacity) {
      const enrolled = typeof pkg.enrolledStudentCount === 'number' ? pkg.enrolledStudentCount : parseInt(String(pkg.enrolledStudentCount));
      const capacity = typeof pkg.studentCapacity === 'number' ? pkg.studentCapacity : parseInt(String(pkg.studentCapacity));
      return Math.min(100, Math.floor((enrolled / capacity) * 100)) + '%';
    }
    return '0%';
  };
  // Popüler paketi belirleme
  const determineIfPopular = (pkg: Package, index: number) => {
    return (
      pkg.name?.toLowerCase().includes('popüler') || 
      pkg.name?.toLowerCase().includes('en çok tercih') ||
      // Birebir için ikinci paket, grup için ilk paket, video için ilk paket
      (pkg.type === 'birebir' && index === 1) ||
      (pkg.type === 'grup' && index === 0) ||
      (pkg.type === 'video' && index === 0)
    );
  };

  // Deneme dersi/paketi belirleme
  const determineIfTrial = (pkg: Package) => {
    return (
      pkg.type === 'trial' ||
      pkg.free === true ||
      pkg.price === 0 ||
      pkg.name?.toLowerCase().includes('deneme') ||
      pkg.name?.toLowerCase().includes('ücretsiz') ||
      pkg.name?.toLowerCase().includes('trial') ||
      pkg.name?.toLowerCase().includes('tanışma') ||
      pkg.discountPercentage === 100
    );
  };

  // İlk ders ücretsiz paketi belirleme
  const determineIfFirstLessonFree = (pkg: Package) => {
    return (
      pkg.name?.toLowerCase().includes('ilk ders ücretsiz') ||
      pkg.name?.toLowerCase().includes('first lesson free') ||
      pkg.description?.toLowerCase().includes('ilk ders ücretsiz') ||
      pkg.features?.some(feature => 
        feature.toLowerCase().includes('ilk ders ücretsiz') ||
        feature.toLowerCase().includes('ücretsiz tanışma')
      )
    );
  };
  
  // Kurs başlama tarihini formatla
  const formatStartDate = (dateString: string | null | undefined) => {
    if (!dateString) return null;
      try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('tr-TR', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      }).format(date);
    } catch {
      return null;
    }
  };

  // Sabit paket önerimleri
  const recommendations = [
    { title: "Hızlı İlerleme", description: "Yapılandırılmış öğretim metodları", icon: Zap, color: "blue" },
    { title: "%95 Başarı Oranı", description: "Kanıtlanmış sonuçlar", icon: Star, color: "green" },
    { title: "Esnek Program", description: "Size uygun saatlerde", icon: TrendingUp, color: "purple" },
  ];
  return (
    <div className="w-full p-3 sm:p-4 lg:p-6 box-border overflow-x-hidden">      {/* Deneme Dersi Vurgusu - Üst Bölüm */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6 bg-gradient-to-r from-yellow-50 via-orange-50 to-red-50 border-2 border-yellow-200 rounded-xl p-4 lg:p-6 relative overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
      >
        <motion.div 
          className="absolute top-0 right-0 w-32 h-32 bg-yellow-100 rounded-full -translate-y-16 translate-x-16 opacity-30"
          animate={{ 
            scale: [1, 1.1, 1],
            rotate: [0, 180, 360]
          }}
          transition={{ 
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
        ></motion.div>
        <motion.div 
          className="absolute bottom-0 left-0 w-24 h-24 bg-orange-100 rounded-full translate-y-12 -translate-x-12 opacity-20"
          animate={{ 
            scale: [1, 0.8, 1],
            x: [-48, -36, -48],
            y: [48, 36, 48]
          }}
          transition={{ 
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        ></motion.div>
        
        <div className="relative z-10 flex flex-col lg:flex-row items-start lg:items-center gap-4">
          <div className="flex-1">
            <motion.div 
              className="flex items-center gap-2 mb-2"
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <motion.div 
                className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center"
                animate={{ 
                  boxShadow: [
                    "0 0 0 0 rgba(245, 158, 11, 0.7)",
                    "0 0 0 10px rgba(245, 158, 11, 0)",
                    "0 0 0 0 rgba(245, 158, 11, 0)"
                  ]
                }}
                transition={{ 
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeOut"
                }}
              >
                <motion.div
                  animate={{ rotate: [0, -15, 15, 0] }}
                  transition={{ 
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <Zap className="w-5 h-5 text-white" />
                </motion.div>
              </motion.div>
              <motion.span 
                className="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold tracking-wide"
                animate={{ 
                  scale: [1, 1.05, 1]
                }}
                transition={{ 
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                ÜCRETSİZ DENEME DERSİ
              </motion.span>
            </motion.div>
            <motion.h3 
              className="text-lg lg:text-xl font-bold text-gray-900 mb-2"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              15 Dakika Ücretsiz Tanışma Dersi Alın!
            </motion.h3>
            <motion.p 
              className="text-gray-700 text-sm lg:text-base leading-relaxed"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              Öğretmenimizle tanışın, Almanca seviyenizi belirleyin ve size özel öğrenim planınızı oluşturun. 
              Hiçbir ödeme yapmadan ilk adımı atın.
            </motion.p>
            <motion.div 
              className="flex flex-wrap gap-3 mt-3"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              {[
                'Seviye belirleme',
                'Özel ders planı', 
                'Öğretmen uyumu'
              ].map((benefit, index) => (
                <motion.div 
                  key={benefit}
                  className="flex items-center gap-2 text-sm text-gray-600"
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                >
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>{benefit}</span>
                </motion.div>
              ))}
            </motion.div>
          </div>
          <motion.div 
            className="lg:ml-4"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.7, type: "spring", stiffness: 200 }}
          >
            <Button 
              onClick={() => {
                const trialPackage = {
                  id: 'trial-lesson',
                  name: 'Ücretsiz Deneme Dersi',
                  description: '15 dakikalık ücretsiz tanışma ve seviye belirleme dersi',
                  price: 0,
                  lessonsInPackage: 1,
                  lessonDurationMinutes: 15,
                  pricePerLessonCalculated: 0,
                  discountPercentage: 100,
                  features: [
                    'Seviye belirleme testi',
                    'Kişiselleştirilmiş öğrenim planı',
                    'Öğretmen tanışması',
                    'Hedef belirleme',
                    'Soru-cevap fırsatı'
                  ],
                  level: 'Tüm Seviyeler',
                  isActive: true,
                  thumbnailUrl: undefined,
                  type: 'trial',
                  free: true,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString()
                };
                onPackageSelect(trialPackage, { price: 0, discount: '100%', name: 'Ücretsiz Deneme' });
              }}
              className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-bold px-6 py-3 rounded-lg text-base lg:text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95"
            >
              <Zap className="w-5 h-5 mr-2" />
              Hemen Başla
            </Button>
          </motion.div>
        </div>
      </motion.div>

      <div className="mb-5 lg:mb-8 w-full">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-3">
          <div>
            <h2 className="text-xl lg:text-2xl font-bold text-gray-900 mb-2">{getFilterTitle()}</h2>
            <p className="text-sm lg:text-base text-gray-600 w-full lg:max-w-3xl">
              {getFilterDescription()}
            </p>
          </div>
          
          {packagesToCompare.length >= 2 && (
            <Button
              onClick={openCompareModal}
              className="mt-3 md:mt-0 bg-blue-50 text-blue-700 hover:bg-blue-100 border border-blue-200"
            >
              <Activity className="w-4 h-4 mr-2" />
              Seçili Paketleri Karşılaştır ({packagesToCompare.length})
            </Button>
          )}
        </div>
        
        {/* Filtreler */}
        <PackageTypeFilter
          packages={allPackages}
          activeFilter={activeFilter}
          activeLevel={activeLevel}
          onFilterChange={setActiveFilter}
          onLevelChange={setActiveLevel}
        />
      </div>

      <AnimatePresence>        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 lg:gap-6 w-full">
          {filteredPackages.length > 0 ? (
            filteredPackages.map((pkg, index) => {
              const IconComponent = packageIcons[pkg.type?.toLowerCase() || pkg.id.toString()] || Target;
              const colors = packageColors[pkg.type?.toLowerCase() || pkg.id.toString()] || {
                gradient: "from-gray-500 to-gray-600",
                bg: "bg-gray-500",
                border: "border-gray-200"
              };
              const isPopular = determineIfPopular(pkg, index);
              const isTrial = determineIfTrial(pkg);
              const isFirstLessonFree = determineIfFirstLessonFree(pkg);
              const isExpanded = expandedPackageIds.includes(pkg.id);
              const isSelectedForComparison = packagesToCompare.some(p => p.id === pkg.id);
              const isGroup = pkg.type?.toLowerCase() === 'grup' || pkg.type === '2';
              const isVideo = pkg.type?.toLowerCase() === 'video' || pkg.type === '3';
              
              return (
                <motion.div 
                  key={pkg.id}
                  layout
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ 
                    type: "spring", 
                    duration: 0.5,
                    delay: index * 0.05
                  }}
                  className="group w-full"
                >
                  <div className={`bg-white border-2 ${isSelectedForComparison ? 'border-blue-400 shadow-md' : 'border-gray-200'} rounded-xl hover:border-gray-300 hover:shadow-lg transition-all duration-300 relative h-full flex flex-col w-full box-border overflow-hidden`}>
                    {/* Paket Thumbnail veya Header */}
                    <div className={`p-4 ${pkg.thumbnailUrl ? 'pt-0 px-0 pb-0' : 'bg-gradient-to-r from-gray-50 to-gray-100'}`}>
                      {pkg.thumbnailUrl ? (
                        <div className="relative w-full h-32 sm:h-36 overflow-hidden rounded-t-lg">                          <Image 
                            src={pkg.thumbnailUrl || '/placeholder.svg'} 
                            alt={pkg.name}
                            width={500}
                            height={200}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/placeholder.svg';
                            }}
                          />
                          <div className="absolute inset-0 bg-black/30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <Button
                              variant="secondary"
                              size="sm"
                              onClick={() => openQuickView(pkg)}
                              className="bg-white/90 hover:bg-white text-gray-800"
                            >
                              <Eye className="w-3.5 h-3.5 mr-1" />
                              Detayları Gör
                            </Button>
                          </div>
                          <div className="absolute top-2 left-2 bg-white/90 px-2 py-1 rounded-md flex items-center">
                            <IconComponent className={`w-3.5 h-3.5 ${isGroup ? 'text-green-600' : isVideo ? 'text-purple-600' : 'text-blue-600'} mr-1`} />
                            <span className="text-xs font-medium">
                              {isGroup ? 'Grup' : isVideo ? 'Video' : 'Birebir'}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <div className="pt-4 px-4 flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className={`w-9 h-9 ${colors.bg} rounded-full flex items-center justify-center shadow-sm`}>
                              <IconComponent className="w-4 h-4 text-white" />
                            </div>
                            <div>
                              <span className="text-xs font-medium text-gray-500">
                                {isGroup ? 'Grup Dersi' : isVideo ? 'Video Kursu' : 'Birebir Ders'}
                              </span>
                              {pkg.level && (
                                <Badge className="ml-2 text-[10px] h-4 py-0 bg-blue-100 text-blue-700 border-0">
                                  {pkg.level.toUpperCase()}
                                </Badge>
                              )}
                            </div>
                          </div>
                          <div 
                            className={`w-5 h-5 rounded-full border ${isSelectedForComparison ? 'border-blue-500 bg-blue-500' : 'border-gray-300 bg-white'} cursor-pointer flex items-center justify-center`}
                            onClick={() => togglePackageComparison(pkg)}
                          >
                            {isSelectedForComparison && (
                              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                                <polyline points="20 6 9 17 4 12"></polyline>
                              </svg>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                      {/* Popüler Badge */}
                    {isPopular && (
                      <div className="absolute -top-1 -right-1 bg-gradient-to-r from-orange-500 to-red-500 text-white py-1 px-3 rounded-br-xl rounded-tl-xl shadow-md z-10 text-xs font-bold">
                        En Popüler
                      </div>
                    )}

                    {/* Deneme Dersi Badge */}
                    {isTrial && (
                      <div className="absolute top-2 left-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white py-1 px-3 rounded-full shadow-md z-10 text-xs font-bold flex items-center gap-1">
                        <Zap className="w-3 h-3" />
                        ÜCRETSİZ DENEME
                      </div>
                    )}

                    {/* İlk Ders Ücretsiz Badge */}
                    {!isTrial && isFirstLessonFree && (
                      <div className="absolute top-2 left-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white py-1 px-3 rounded-full shadow-md z-10 text-xs font-bold">
                        İlk Ders Ücretsiz
                      </div>
                    )}

                    <div className="p-4 flex-1 flex flex-col">
                      <h3 className="text-lg font-bold text-gray-900 mb-1 line-clamp-2 min-h-[2.75rem]">{pkg.name}</h3>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2 flex-grow">{pkg.shortDesc || pkg.description}</p>
                      
                      {/* Grup Kursu Bilgileri */}
                      {isGroup && (
                        <div className={`mb-3 p-2 rounded-lg bg-green-50 border ${colors.border}`}>
                          {pkg.groupCourseStartDate && (
                            <div className="flex items-center gap-2 mb-1.5">
                              <Calendar className="w-4 h-4 text-green-600" />
                              <div>
                                <span className="text-xs font-medium text-gray-700">Başlangıç:</span>
                                <span className="text-xs ml-1">{formatStartDate(pkg.groupCourseStartDate)}</span>
                              </div>
                            </div>
                          )}
                          
                          {pkg.groupCourseSchedule && (
                            <div className="flex items-center gap-2 mb-1.5">
                              <CalendarClock className="w-4 h-4 text-green-600" />
                              <div>
                                <span className="text-xs font-medium text-gray-700">Program:</span>
                                <span className="text-xs ml-1">{pkg.groupCourseSchedule}</span>
                              </div>
                            </div>
                          )}
                          
                          {(pkg.enrolledStudentCount !== undefined && pkg.studentCapacity !== undefined) && (
                            <div className="flex items-center gap-2">
                              <Users className="w-4 h-4 text-green-600" />
                              <div className="flex-1">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="text-xs font-medium text-gray-700">Doluluk:</span>
                                  <span className="text-xs">{pkg.enrolledStudentCount}/{pkg.studentCapacity}</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-1.5">
                                  <div 
                                    className="bg-green-500 h-1.5 rounded-full"
                                    style={{ width: calculateFilledPercentage(pkg) }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                      
                      {/* Video Kursu Bilgileri */}
                      {isVideo && pkg.videoCourseTotalLessons && (
                        <div className={`mb-3 p-2 rounded-lg bg-purple-50 border ${colors.border}`}>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Video className="w-4 h-4 text-purple-600" />
                              <span className="text-xs font-medium text-gray-700">{pkg.videoCourseTotalLessons} Video Ders</span>
                            </div>
                            
                            {pkg.videoCourseTotalHours && (
                              <div className="flex items-center gap-1">
                                <Clock className="w-3.5 h-3.5 text-purple-600" />
                                <span className="text-xs font-medium text-gray-700">{pkg.videoCourseTotalHours} saat</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Özellikler - Minimal Gösterim */}
                      {pkg.features && pkg.features.length > 0 && (
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-sm font-medium text-gray-700">Bu paket şunları içerir:</h4>
                            {pkg.features.length > 3 && (
                              <Button
                                variant="link"
                                size="sm" 
                                className="text-xs p-0 h-auto text-blue-600"
                                onClick={() => togglePackageDetails(pkg.id)}
                              >
                                {isExpanded ? (
                                  <>
                                    <ChevronUp className="w-3 h-3 mr-1" />
                                    Az göster
                                  </>
                                ) : (
                                  <>
                                    <ChevronDown className="w-3 h-3 mr-1" />
                                    Tümü ({pkg.features.length})
                                  </>
                                )}
                              </Button>
                            )}
                          </div>
                          <ul className="space-y-1.5">
                            {pkg.features.slice(0, isExpanded ? pkg.features.length : 3).map((feature, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <CheckCircle className="w-3.5 h-3.5 text-green-500 mt-0.5 flex-shrink-0" />
                                <span className="text-gray-700 text-xs">{feature}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}                      {/* Fiyat ve Seçenekler */}
                      <div className="mt-auto">
                        {/* Deneme Dersi Özel Fiyat Gösterimi */}
                        {isTrial ? (
                          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-200 rounded-lg p-3 mb-3">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <Zap className="w-4 h-4 text-yellow-600" />
                                <span className="font-bold text-gray-900">Ücretsiz Deneme</span>
                              </div>
                              <Badge className="bg-yellow-100 text-yellow-800 text-xs font-bold">%100 İNDİRİM</Badge>
                            </div>
                            <div className="flex justify-between items-end">
                              <div>
                                <span className="text-2xl font-bold text-green-600">₺0</span>
                                <span className="text-sm line-through text-gray-500 ml-2">₺{pkg.pricePerLessonCalculated || 200}</span>
                              </div>
                              <Button
                                onClick={() => onPackageSelect(pkg, { price: 0, discount: '100%', name: 'Ücretsiz Deneme' })}
                                className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-bold px-4 py-2 text-sm"
                              >
                                <Zap className="w-4 h-4 mr-1" />
                                Hemen Al
                              </Button>
                            </div>
                          </div>
                        ) : pkg.options && pkg.options.length > 0 ? (
                          <div className="space-y-2 w-full">
                            {pkg.options.slice(0, 1).map((option: PackageOption, optionIndex: number) => (
                              <div key={optionIndex} className={`border border-gray-200 rounded-lg p-3 hover:border-${isGroup ? 'green' : isVideo ? 'purple' : 'blue'}-300 transition-colors w-full`}>
                                <div className="flex justify-between items-center mb-2">
                                  <div className="flex items-center gap-2">
                                    <span className="font-bold text-gray-900">{option.sessions || option.name || '1'} Ders</span>
                                    {option.duration && (
                                      <span className="text-xs text-gray-500 flex items-center">
                                        <Clock className="w-3 h-3 mr-0.5" />
                                        {option.duration}
                                      </span>
                                    )}
                                  </div>
                                  {option.discount && (
                                    <Badge className="bg-red-100 text-red-700 text-xs">%{option.discount}</Badge>
                                  )}
                                </div>                                <div className="flex justify-between items-end">
                                  <div>
                                    <span className="text-xl font-bold text-gray-900">₺{option.price}</span>
                                    {option.perLesson && <span className="text-xs text-gray-500 ml-1">(₺{option.perLesson}/ders)</span>}
                                    {/* İlk Ders Ücretsiz Vurgusu */}
                                    {isFirstLessonFree && (
                                      <div className="mt-1">
                                        <span className="inline-flex items-center gap-1 bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded-full">
                                          <Zap className="w-3 h-3" />
                                          İlk ders ücretsiz
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                  <div className="flex gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => openQuickView(pkg)}
                                      className="text-xs px-2"
                                    >
                                      <Eye className="w-3 h-3 mr-1" />
                                      Detay
                                    </Button>
                                    <Button
                                      onClick={() => onPackageSelect(pkg, option)}
                                      className={`bg-gradient-to-r ${colors.gradient} hover:opacity-90 text-white text-xs px-3`}
                                    >
                                      <CreditCard className="w-3 h-3 mr-1" />
                                      Seç
                                    </Button>
                                  </div>
                                </div>
                                  {pkg.options && pkg.options.length > 1 && (
                                  <div className="mt-2 pt-2 border-t border-gray-100 text-center">
                                    <Button
                                      variant="link"
                                      size="sm"
                                      onClick={() => openQuickView(pkg)}
                                      className="text-xs text-gray-500"
                                    >
                                      + {pkg.options.length - 1} farklı paket daha var
                                    </Button>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>                        ) : (
                          <div className="border border-gray-200 rounded-lg p-3 w-full">
                            <div className="flex justify-between items-center">
                              <div>
                                <span className="text-xl font-bold text-gray-900">₺{pkg.price}</span>
                                {/* İlk Ders Ücretsiz Vurgusu */}
                                {isFirstLessonFree && (
                                  <div className="mt-1">
                                    <span className="inline-flex items-center gap-1 bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded-full">
                                      <Zap className="w-3 h-3" />
                                      İlk ders ücretsiz
                                    </span>
                                  </div>
                                )}
                              </div>
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => openQuickView(pkg)}
                                  className="text-xs px-2"
                                >
                                  <Eye className="w-3 h-3 mr-1" />
                                  Detay
                                </Button>
                                <Button
                                  onClick={() => onPackageSelect(pkg, { price: pkg.price })}
                                  className={`bg-gradient-to-r ${colors.gradient} hover:opacity-90 text-white text-xs px-3`}
                                >
                                  <CreditCard className="w-3 h-3 mr-1" />
                                  Seç
                                </Button>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })
          ) : (
            <div className="col-span-1 md:col-span-2 lg:col-span-3 p-8 bg-gray-50 rounded-xl border border-gray-200 text-center">
              <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <SearchIcon className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Seçilen kriterlere uygun paket bulunamadı</h3>
              <p className="text-gray-600 mb-4">Farklı bir filtre seçimi yaparak diğer paketlere göz atabilirsiniz.</p>
              <Button 
                onClick={() => {
                  setActiveFilter('all'); 
                  setActiveLevel('all');
                }}
                variant="outline"
              >
                Tüm Paketleri Göster
              </Button>
            </div>
          )}
        </div>
      </AnimatePresence>

      {/* Öneriler */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 lg:gap-4 mt-8 lg:mt-10 mb-6 lg:mb-8 w-full">
        {recommendations.map((rec, index) => {
          const IconComponent = rec.icon;
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 + 0.3 }}
              className={`bg-gradient-to-r from-${rec.color}-50 to-${rec.color}-100 p-3 lg:p-4 rounded-xl border border-${rec.color}-200 flex flex-col h-full`}
            >
              <div className="flex items-center gap-3 flex-grow">
                <div className={`w-8 h-8 lg:w-10 lg:h-10 bg-${rec.color}-500 rounded-full flex items-center justify-center flex-shrink-0`}>
                  <IconComponent className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
                </div>
                <div>
                  <h3 className={`font-bold text-${rec.color}-900 text-sm lg:text-base`}>{rec.title}</h3>
                  <p className={`text-${rec.color}-700 text-xs lg:text-sm`}>{rec.description}</p>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Ücretsiz Danışmanlık CTA */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.5 }}
        className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-4 lg:p-6 text-white text-center"
      >
        <h3 className="text-lg lg:text-xl font-bold mb-2 lg:mb-3">Hangi kursun size uygun olduğundan emin değil misiniz?</h3>
        <p className="text-blue-100 mb-3 lg:mb-4 text-sm">15 dakikalık ücretsiz danışmanlık seansında size en uygun programı belirleyelim.</p>
        <Button 
          onClick={() => {
            // Ücretsiz danışmanlık paketi
            const consultationPackage = {
              ...allPackages[0], // Mevcut bir paketi klonla
              id: 'free-consultation',
              name: 'Ücretsiz Danışmanlık',
              price: 0,
              type: 'birebir',
              description: '15 dakikalık ücretsiz danışmanlık seansı',
              features: ['Almanca seviyenizi belirleme', 'Size özel ders planı oluşturma', 'Öğrenim sürecini planlama']
            };
            onPackageSelect(consultationPackage, { price: 0, discount: '100%' });
          }}
          className="bg-white text-blue-600 hover:bg-gray-100 font-semibold px-4 lg:px-6 py-2 text-sm"
        >
          Ücretsiz Danışmanlık Al
        </Button>
      </motion.div>

      {/* Modaller */}
      {showCompareModal && (
        <PackageCompareModal
          isOpen={showCompareModal}
          onClose={closeCompareModal}
          packages={packagesToCompare}
        />
      )}
      
      {quickViewIsOpen && quickViewPackage && (
        <PackageQuickView
          isOpen={quickViewIsOpen}
          onClose={closeQuickView}
          pkg={quickViewPackage}
          onPackageSelect={onPackageSelect}
          teacher={teacher}
        />
      )}
    </div>
  );
};
