# Öğretmen Profil Sayfası Geliştirme ve Test Planı

Bu belge, AlmancaABC platformunda modern, kullanıcı dostu ve rakip analizleriyle güçlendirilmiş bir öğretmen profil sayfası (`/ogretmenler/[id]`) geliştirme sürecini ve yapılacakları içerir. Amacımız; öğrencilerin öğretmenler hakkında detaylı bilgiye ulaşabilmesi, kolayca ders talebinde bulunabilmesi ve öğretmenlerin kendilerini en iyi şekilde tanıtabileceği, güven veren, mobil uyumlu ve SEO dostu bir profil deneyimi sunmaktır. Çalışma yalnızca öğretmen detay sayfasını kapsar, ana sayfa bu sürece dahil değildir.

## 0. Öğretmen Profil Sistemi Yapısı
- [x] Öğretmen kaydolma, bilgi girme ve yayınlanma süreci tasarlandı.
- [ ] Öğretmen kaydolduktan sonra otomatik profil sayfası oluşturma akışı uygulanmalı:
  - Öğretmen kaydı sonrası profil sayfasının hemen kullanılabilir olması
  - Eksik bilgilerin varsayılan değerlerle doldurulması
  - Admin onayı/kontrolü için test edilebilir profil önizlemesi
- [ ] Öğretmenlere profil onay/gözden geçirme aşamasında ön izleme imkanı tanınmalı.
- [ ] `is_approved` ve `is_visible` parametrelerine göre listeleme ve URL oluşturma mantığı kontrol edilmeli:
  - `is_approved = true` ve `is_visible = true` ise: Öğretmen ana sayfada gösterilir ve `/ogretmenler/[id]` URL'i aktif olur
  - Diğer durumlarda profil taslak olarak kalır veya "onay bekliyor" durumunda görüntülenir
- [ ] Ana sayfa ve öğretmen listesi sayfasında doğru öğretmenlerin gösterilmesi mantığı test edilmeli

## 1. Önceki Notlar ve Entegrasyon Adımları
- [x] Modern tasarım ve bileşenler aktarıldı.
- [x] Yardımcı dosyalar, tipler ve hook'lar kopyalandı ve güncellendi.
- [x] UI bileşenleri ve ana/alt bileşenler entegre edildi.
- [x] Stil dosyaları ve provider'lar kontrol edildi.
- [x] Backend (Prisma şeması, migrasyon, server actions) güncellendi.
- [x] Veri akışı ve tip hatalarının çoğu giderildi, ancak bazı alanlarda (ör. `oneOnOnePackages`, `description`) tip uyumsuzlukları devam edebilir.
- [x] SEO, meta ve Schema.org entegrasyonu tamamlandı.
- [x] SSS bölümü eklendi ve tab navigasyonuna entegre edildi.
- [x] Benzer öğretmenler bölümü eklendi (TeacherRecommendations komponenti).
- [ ] Kapsamlı test ve mobil uyumluluk kontrolü yapılmalı.

## 2. Profil Bilgileri
- [ ] Öğretmen adı, fotoğrafı, başlık, kısa tanıtım eksiksiz ve tekil gösterilmeli.
- [x] İstatistikler (toplam ders, puan, öğrenci, deneyim) doğru ve eksiksiz gösterilmeli, "undefined" ve "0+" gibi hatalar giderildi.
- [ ] Profil fotoğrafı büyütme ve galeri görüntüleme özelliği eklenmeli.
- [ ] Çevrimiçi/çevrimdışı durumu gerçek zamanlı güncellenmeli.
- [ ] Akademik geçmiş ve eğitim bilgileri zaman çizelgesi şeklinde düzenlenmeli.
- [ ] Sertifikalar ve belgeler için ayrı bir bölüm oluşturulmalı.
- [ ] Eğitim ve yetkinlik alanlarına göre yetenek çubuğu/grafiği eklenmeli.
- [ ] Bio/hakkında bölümü için düzgün formatlama ve uzun/kısa görünüm opsiyonu sunulmalı.

## 3. Kurslar ve Paketler
- [x] Tüm paketler ve kurslar detaylı, fiyatlı ve açıklamalı şekilde listelendi.
- [x] "Seç" butonları rezervasyon modalına bağlandı ve işlevsel hale getirildi.
- [x] Birebir, grup ve video kursları için farklı renkler ve ikonlar ile ayırt edici tasarım eklendi.
- [x] Her paket/kurs için detay açma/kapama özelliği eklendi.
- [x] Paket karşılaştırma özelliği eklendi (en fazla 3 paket).
- [x] İndirimli fiyatların ve tasarruf yüzdelerinin belirgin gösterimi eklendi.
- [x] "En Popüler" etiketi öne çıkan kurslar için dinamik olarak eklendi.
- [x] Ücretsiz danışmanlık paketi için özelleştirilmiş UI eklendi.
- [x] Paket/kurs tipi ve seviye filtreleme özelliği eklendi.
- [x] Görsel odaklı modern paket kartları tasarlandı.
- [x] Grup kursları için doluluk oranı göstergesi eklendi.
- [x] Kurs detayları için hızlı inceleme modalı eklendi.
- [x] Paket karşılaştırma özelliği görsel ve bilgilendirici hale getirildi.
- [x] İyileştirilmiş mobil deneyimi için responsive tasarım güncellendi.
- [x] Animation ve micro-interactions ile UX zenginleştirildi.
- [x] Birebir özel dersler için tek ders veya çoklu ders seçeneği eklendi.
- [x] İlk dersi ücretsiz yapma seçeneği ile yeni öğrencileri çekme stratejisi eklendi.
- [x] Deneme dersi/paketi vurgusu özelliği modern ve dikkat çekici şekilde eklendi.

## 4. Rezervasyon ve Mesajlaşma
- [x] "Randevu Al" ve "Mesaj Gönder" butonları işlevsel hale getirildi ve ilgili modallara bağlandı.
- [x] "Mesaj Gönder" butonu için form validasyonu ve gönderim fonksiyonalitesi eklendi.
- [x] Rezervasyon modalında tarih/saat seçimi için takvim entegrasyonu tamamlandı.
- [x] Seçilen pakete göre rezervasyon seçenekleri eklendi (TeacherBookingCalendarModal).
- [x] Mesaj gönderme formu için gerçek zamanlı validasyon ve gönderim işlemi tamamlandı.
- [x] Rezervasyon başarılı mesajı ve sonraki adımlar bilgisi düzenlendi.
- [ ] Rezervasyon sonrası e-posta bildirimleri için entegrasyon (future)

## 5. Video Tanıtım
- [x] Tanıtım videosu alanı eklendi ve çalışır hale getirildi.
- [x] Video butonunun profil banner alanında daha belirgin hale getirilmesi tamamlandı.
- [x] TeacherVideosTab bileşeninin video gösterimi iyileştirildi.
- [x] Video önizleme/thumbnail sisteminin eklenmesi tamamlandı.
- [x] YouTube/Vimeo entegrasyonu için embed linki oluşturma ve doğrulama eklendi.
- [ ] Video yükleme/değiştirme arayüzünün öğretmen dashboard'ına entegrasyonu yapılacak.

## 6. SSS ve Ek Bilgiler
- [x] Sıkça Sorulan Sorular (SSS) bölümü eklendi ve tab navigasyonına entegre edildi.
- [x] Benzer öğretmenler veya öneriler bölümü eklendi (TeacherRecommendations komponenti).

## 7. Sosyal Medya ve Güven Unsurları
- [x] Sosyal medya linkleri ve güven rozetleri eklendi ve profil sayfasına entegre edildi.
- [x] Öğretmen sosyal medya hesapları için ikonlar ve linkler eklendi (SocialMediaLinks komponenti).
- [x] Platform içi "Doğrulanmış Öğretmen", "Sertifikalı Öğretmen", "Deneyimli Öğretmen" ve "Süper Öğretmen" rozetleri eklendi.
- [x] Profilde kimlik ve sertifika doğrulama rozetleri eklendi (TeacherTrustBadges komponenti).
- [ ] Paylaşım butonları (Facebook, Twitter, WhatsApp, vb.) daha belirgin hale getirilecek.
- [ ] "Bu Öğretmeni Arkadaşına Öner" butonu ve e-posta formu eklenmeli.
- [ ] Topluluk istatistikleri ("30+ öğretmen tarafından tavsiye ediliyor" vb.)

## 8. Mobil ve Erişilebilirlik
- [x] Sayfa mobilde ve farklı ekranlarda düzgün çalışacak şekilde responsive düzenlemeler yapıldı.
- [x] Responsive tasarım için ekran boyutlarına göre özelleştirilmiş görünümler eklendi:
  - Mobil: (<768px) - Tek kolon görünüm, küçültülmüş resimler, özelleştirilmiş menü
  - Tablet: (768px-1023px) - Ortalama resimler, 2 kolon kurslar
  - Masaüstü: (>1024px) - Tam görünüm, 3+ kolon kurslar
- [x] Eksik veya kırık görsel bağlantıları için fallback mekanizması eklendi.
- [ ] Erişilebilirlik (a11y) kontrolleri yapılmalı.
- [ ] Dokunmatik cihazlar için etkileşim alanlarının yeterli büyüklükte olması.
- [ ] WCAG 2.1 AA seviyesi uyumluluk kontrolleri:
  - Ekran okuyucu uyumluluğu
  - Klavye navigasyonu
  - Renk kontrastı
  - Alt metinler ve açıklamalar
- [ ] Sayfanın yüklenme performansı ve hızı ölçülmeli ve optimize edilmeli.
- [ ] Bağlantı hızı ve düşük bant genişliği koşullarında deneyim kontrol edilmeli.

## 9. Hatalar ve Eksikler
- [x] "undefined" ve "0+" gibi hatalar düzeltildi (TeacherStatsBar komponenti güncellendi).
- [x] TypeScript tip uyumsuzlukları düzeltildi (TeacherProfileClientData ile alt bileşenler uyumlu hale getirildi).
- [x] Video URL'lerinin doğrulanması ve broken image kontrolleri eklendi.
- [x] Eksik fotoğraflar için varsayılan imajlar ve hata yakalama eklendi.
- [x] Next.js image konfigürasyonu harici görsel domainleri için güncellendi (placekitten.com).
- [ ] Tüm alanlar dolu ve anlamlı olmalı.
- [ ] Eksik paket bilgilerinde yerine "Henüz paket eklenmemiş" gibi bilgi mesajları gösterilmeli.
- [ ] Eksik profil alanlarının editorler tarafından güncellenmesi için hızlı düzenleme linkleri eklenmeli.
- [ ] TeacherProfileClient ve alt bileşenler için null/undefined kontrolleri güçlendirilmeli.
- [ ] Hata yakalama ve loglama mekanizmaları geliştirilmeli.
- [ ] End-to-end testler ve kullanıcı senaryoları oluşturulmalı.

---

## Test Edilecekler
- [ ] Tüm sekmeler ve butonlar çalışıyor mu?
- [ ] Modal ve rezervasyon akışı sorunsuz mu?
- [ ] Profildeki tüm bilgiler doğru ve eksiksiz mi?
- [ ] Mobilde ve masaüstünde görünüm düzgün mü?
- [ ] SEO ve meta etiketleri doğru mu?
- [ ] Yorumlar, video, SSS ve benzer öğretmenler bölümleri var mı ve çalışıyor mu?

## Rakip Analizi Notları
- **italki:**
  - Kapsamlı öğretmen biyografisi, video tanıtım, detaylı istatistikler (toplam ders, öğrenci, puan, katılım oranı, cevap oranı), çoklu dil desteği, deneme dersi, paketler, öğrenci yorumları, kişiselleştirilmiş dersler, materyal listesi, uygunluk takvimi, güven unsurları (doğrulanmış öğretmen, rozetler), sosyal medya ve paylaşım, detaylı SSS, benzer öğretmenler.
- **ozelders.com:**
  - Kısa biyografi, eğitim geçmişi, ders verdiği alanlar, kitle, mekan, ders şekli, fiyatlar, uygunluk zamanları, deneyim, kişisel bilgiler, sunduğu hizmetler ve kolaylıklar (ücretsiz ilk ders, grup indirimi), iletişim, sosyal medya, başarı hikayeleri, öğrenci yorumları, şikayet bildirimi, favorilere ekleme.
- **preply.com:**
  - Uzun biyografi, sertifikalar, uzmanlık alanları, video tanıtım, detaylı istatistikler (toplam ders, puan, öğrenci, cevap süresi), deneme dersi, paketler, öğrenci yorumları, benzer öğretmenler, sosyal medya, SSS, blog, uygulama, güven unsurları (süper öğretmen, profesyonel öğretmen, rozetler), detaylı filtreleme.
- **ozeldersalani.com:**
  - Kapsamlı biyografi, eğitim ve sertifika bilgisi, sunduğu imkanlar (ders kaydı, materyal, grup indirimi), ders işleyişi, fiyatlar, öğrenci yorumları, referanslar, başarı hikayeleri, sosyal medya, blog, SSS, benzer öğretmenler.
- **superprof.com.tr:**
  - Kapsamlı biyografi, ders mekanları, lider rozetleri, kişiselleştirilmiş eğitim, hedef odaklı programlar, fiyatlar, öğrenci yorumları ve referanslar, benzer öğretmenler, sosyal medya, güven unsurları, detaylı filtreleme, SSS.
- **armut.com:**
  - Temel profil, kısa biyografi, hizmetler, fiyatlar, iletişim, sosyal medya, güven unsurları.

## Rakiplerden Eksik Olan ve Eklenecekler
- [x] Video tanıtım (kısa ve kişisel) - Eklendi ve çalışır durumda
- [x] Detaylı istatistikler (toplam ders, öğrenci, puan, cevap oranı, katılım oranı) - TeacherStatsBar güncellendi
- [x] Deneme dersi/paketi vurgusu - Modern ve dikkat çekici şekilde eklendi, rakiplerle eşdeğer seviyede
- [ ] Kapsamlı öğrenci yorumları ve referanslar
- [ ] Kapsamlı biyografi ve uzmanlık alanları
- [ ] Sertifika ve eğitim geçmişi
- [ ] Kişiselleştirilmiş ders ve materyal listesi
- [ ] Uygunluk takvimi ve hızlı rezervasyon
- [ ] Güven unsurları (rozetler, doğrulama, platform garantisi)
- [x] SSS ve blog entegrasyonu - SSS tab eklendi
- [x] Benzer öğretmenler/öneriler - TeacherRecommendations komponenti eklendi
- [ ] Sosyal medya ve paylaşım butonları
- [ ] Mobilde ve masaüstünde modern, sade, hızlı UX
- [ ] Detaylı filtreleme ve arama
- [x] SEO ve Schema.org yapısal veri - generateMetadata ve Schema.org JSON-LD eklendi

## Son Yapılan Güncellemeler (23 Aralık 2025)
- [x] Deneme dersi/paketi vurgusu özelliği tamamlandı:
  - TeacherCoursesTab'a özel deneme dersi vurgu bölümü eklendi
  - Ücretsiz 15 dakikalık tanışma dersi için özel tasarım ve CTA eklendi
  - Paket kartlarında "ÜCRETSİZ DENEME" ve "İlk Ders Ücretsiz" etiketleri eklendi
  - Deneme dersi belirleme mantığı (determineIfTrial ve determineIfFirstLessonFree) eklendi
  - Fiyat gösteriminde deneme dersleri için özel %100 indirim vurgusu eklendi
  - Normal paketlerde "İlk ders ücretsiz" özelliği için yeşil vurgu eklendi
  - TeacherAboutTab'daki deneme dersi kartı daha etkileyici gradient tasarımla güncellendi
  - Mock data'ya ücretsiz deneme dersi paketi örneği eklendi
- [x] Deneme dersi deneyimi rakiplerle (italki, preply, ozelders.com) eşdeğer seviyeye getirildi
- [x] Öğrenci kazanımını artırmak için güçlü CTA (Call-to-Action) butonları eklendi

## Son Yapılan Güncellemeler (1 Temmuz 2025)
- [x] Video tanıtım alanı iyileştirmeleri tamamlandı:
  - TeacherProfileHero bileşenine belirgin video butonları eklendi
  - Video modal bileşeni geliştirildi ve profil sayfasına entegre edildi
  - TeacherVideosTab bileşeni görsel olarak iyileştirildi
  - YouTube video önizlemelerinin otomatik oluşturulması için fonksiyon eklendi
  - Hem tanıtım videosu hem de kurs videoları için arayüzler geliştirildi
- [x] Rezervasyon ve mesajlaşma sistemi tamamlandı:
  - TeacherMessageModal bileşeni oluşturuldu ve entegre edildi
  - Mesaj formuna gerçek zamanlı validasyon ve gönderim işlevi eklendi
  - "Randevu Al" ve "Mesaj Gönder" butonları işlevsel hale getirildi
  - Rezervasyon modalında takvim ve saat seçimi iyileştirildi
- [x] Kurslar ve paketler bölümü geliştirildi:
  - Paket karşılaştırma özelliği (PackageCompareModal) eklendi
  - "En Popüler" etiketi gösterimi eklendi
  - Paket detayları açma/kapama fonksiyonu eklendi
  - İndirim ve tasarruf bilgileri belirginleştirildi
  - Ücretsiz danışmanlık paketi için özel UI eklendi
- [x] VideoPlayerModal bileşeni genişletildi:
  - Tanıtım videoları ve kurs videoları arasında geçiş için altyapı kuruldu
  - Öğretmen tanıtım videosu öncelikli gösterim için düzenlendi
- [x] Öğretmen profil sistemi planlama ve entegrasyon adımları detaylandırıldı
- [x] Güncellenmiş plan ve önceliklendirilmiş görevler eklendi

## Son Yapılan Güncellemeler (23 Haziran 2025)

### Deneme Dersi/Paketi Vurgusu Özelliği Tamamlandı ✅
- [x] TeacherCoursesTab'a özel deneme dersi vurgu bölümü eklendi (üst banner)
- [x] Paket kartlarında "ÜCRETSİZ DENEME" ve "İlk Ders Ücretsiz" etiketleri eklendi
- [x] Fiyat gösteriminde deneme dersleri için özel vurgu ve %100 indirim badge'i
- [x] TeacherAboutTab'daki deneme dersi kartı güncellendi ve görsel olarak iyileştirildi
- [x] TeacherProfileHero'ya büyük deneme dersi CTA bölümü eklendi
- [x] Prisma schema'ya deneme dersi alanları eklendi (isTrialLesson, isFirstLessonFree, etc.)
- [x] TrialLessonModal bileşeni oluşturuldu - kapsamlı rezervasyon süreci
- [x] Animation ve micro-interactions ile deneyim zenginleştirildi
- [x] Mobil responsive tasarım ile tüm cihazlarda optimum görünüm
- [x] useTeacherData hook'una deneme dersi örnekleri eklendi
- [x] Geliştirme sunucusu başlatıldı (`bun run dev` komutu çalıştırıldı)

### Yeni Eklenen Özellikler
- **Akıllı Deneme Dersi Tanıma**: Paketlerde otomatik deneme dersi tespiti
- **3 Aşamalı Rezervasyon Modali**: Bilgilendirme → Form → Başarı sayfası
- **Gelişmiş Animasyonlar**: Pulse, rotate, scale efektleri ile dikkat çekici tasarım
- **Seviye Belirleme Sistemi**: 8 farklı Almanca seviyesi seçeneği
- **Öğrenme Hedefi Analizi**: 8 farklı öğrenim motivasyonu kategorisi
- **Zaman Tercihi Sistemi**: Hafta içi/sonu, sabah/öğle/akşam seçenekleri

### Teknik İyileştirmeler
- TypeScript tip güvenliği ile hata önleme
- Next.js Image componenti ile performans optimizasyonu  
- Framer Motion ile smooth animasyonlar
- Tailwind CSS ile responsive tasarım
- Form validasyonu ve error handling

### Sonraki Adımlar (Opsiyonel)
- [ ] Backend API entegrasyonu için server actions
- [ ] E-posta notification sistemi
- [ ] Deneme dersi analytics ve tracking
- [ ] A/B testing için farklı vurgu türleri
- [ ] WhatsApp/SMS bildirim entegrasyonu
- [x] Responsive tasarım iyileştirmeleri yapıldı:
  - TeacherCoursesTab bileşeninde grid düzeni mobil cihazlar için optimize edildi
  - TeacherVideosTab bileşeninde responsive grid yapısı güncellendi
  - TeacherProfileHero bileşeninde başlık ve metinler mobil ekranlar için düzeltildi
  - Görsel boyutları ve yerleşimleri farklı ekran boyutlarına göre ayarlandı
- [x] Eksik ve kırık görseller için çözümler eklendi:
  - Bütün Image bileşenlerine fallback mekanizması eklendi
  - Kurs/video kart görselleri için error handling eklendi
  - Yerleşik fallback imajları (/placeholder.svg) tüm alanlara entegre edildi
- [x] next.config.ts dosyasında harici görsel domainleri güncellendi:
  - placekitten.com domaininin kullanılabilmesi için remotePatterns'e eklendi
  - Kod formatı ve yapısal hatalar düzeltildi
- [x] Tüm bileşen hiyerarşisinde responsive design kontrolleri yapıldı
- [x] Sosyal medya ve güven unsurları eklendi:
  - TeacherTrustBadges komponenti oluşturuldu ve entegre edildi
  - SocialMediaLinks komponenti oluşturuldu ve entegre edildi
  - "Doğrulanmış Öğretmen", "Sertifikalı Öğretmen", "Deneyimli Öğretmen" ve "Süper Öğretmen" rozetleri eklendi
  - TeacherProfileClientData tipi sosyal medya alanları için güncellendi

## Yapılacak İşlemler Sıralaması ve Önceliklendirme

### Kritik Önem (İlk Sprint - Mevcut)
1. [x] Video tanıtım alanının iyileştirilmesi ve belirgin hale getirilmesi ✅
2. [x] "Randevu Al" ve "Mesaj Gönder" butonlarının işlevselleştirilmesi ✅
3. [x] Paketlerin doğru ve eksiksiz gösterilmesi ✅
4. [x] Responsiv tasarım ve mobil uyumluluk iyileştirmeleri ✅
5. [x] Eksik görsel ve broken image hatalarının giderilmesi ✅
6. [x] Özel ders seçiminde tek/çoklu ders seçeneği ve ilk ders ücretsiz opsiyonu ✅
7. [ ] Öğretmen profil oluşturma ve onay sürecinin düzenlenmesi
8. [ ] Kalan hatalar ve eksik içeriklerin düzeltilmesi
9. [ ] Ana sayfa ve öğretmen listesi sayfalarında doğru entegrasyon

### Yüksek Önem (İkinci Sprint)
1. [ ] Yorum ve değerlendirme sisteminin geliştirilmesi
2. [x] Sosyal medya entegrasyonu ve paylaşım butonları ✅
3. [x] Öğretmen doğrulama ve güven unsurlarının eklenmesi ✅
4. [x] Mobil uyumluluk ve responsive tasarım optimizasyonu ✅
5. [x] Rezervasyon modalı ve takvim entegrasyonu ✅

### Orta Önem (Üçüncü Sprint)
1. [ ] Profil bilgileri görünümünün zenginleştirilmesi
2. [ ] Eğitim ve sertifikalar için detaylı gösterim
3. [ ] Benzer öğretmenler algoritmasının geliştirilmesi
4. [ ] Erişilebilirlik (a11y) iyileştirmeleri

### Düşük Önem (İleride Planlanacak)
1. [ ] Gelişmiş filtreleme ve arama özellikleri
2. [ ] İstatistik ve metrik gösterimlerinin detaylandırılması
3. [ ] Performans optimizasyonları ve lazy-loading
4. [ ] SEO ve analitik entegrasyonu geliştirmeleri

## 4. Yorumlar ve Değerlendirmeler
- [ ] Öğrenci yorumları ve puanlama bölümü eklenmeli/görünür olmalı.
- [ ] Yorum filtreleme ve sıralama seçenekleri eklenmeli (en yeni, en yüksek puan, vb.).
- [ ] Yorum sayfalaması (pagination) eklenmeli.
- [ ] Ortalama puan ve puan dağılımı grafiği gösterilmeli.
- [ ] Yorumlara öğretmen yanıtları için özel alan ayrılmalı.
- [ ] Yararlı/Yararsız değerlendirme tuşları eklenmeli.
- [ ] Yorum doğrulama rozeti gösterimi ("Doğrulanmış Öğrenci" vb.).
