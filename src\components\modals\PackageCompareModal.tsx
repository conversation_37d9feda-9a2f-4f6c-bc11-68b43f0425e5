import React from 'react';
import { GitCompare, X, CheckCircle, AlertCircle, Clock, CreditCard, Target, Users, Video } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AnimatePresence, motion } from 'framer-motion';

interface PackageCompareModalProps {
  isOpen: boolean;
  onClose: () => void;
  packages: Array<{
    id: string | number;
    name: string;
    price?: string | number;
    shortDesc?: string;
    description?: string;
    features?: string[];
    options?: Array<{
      price: string | number;
      sessions?: number;
      duration?: string;
      perLesson?: number | string;
      discount?: string | null;
    }>;
    level?: string;
    type?: string;
    lessonDurationMinutes?: number | null;
  }>;
}

const PackageCompareModal: React.FC<PackageCompareModalProps> = ({ 
  isOpen, 
  onClose, 
  packages 
}) => {
  if (!isOpen) return null;

  // Paket tiplerine göre renkler ve ikonlar
  const packageIcons: { [key: string]: React.ElementType } = {
    '1': Target,
    'birebir': Target,
    '2': Users,
    'grup': Users,
    '3': Video,
    'video': Video
  };

  const packageColors: { [key: string]: { bg: string, light: string, border: string } } = {
    '1': { bg: "bg-blue-500", light: "bg-blue-50", border: "border-blue-200" },
    'birebir': { bg: "bg-blue-500", light: "bg-blue-50", border: "border-blue-200" },
    '2': { bg: "bg-green-500", light: "bg-green-50", border: "border-green-200" },
    'grup': { bg: "bg-green-500", light: "bg-green-50", border: "border-green-200" },
    '3': { bg: "bg-purple-500", light: "bg-purple-50", border: "border-purple-200" },
    'video': { bg: "bg-purple-500", light: "bg-purple-50", border: "border-purple-200" }
  };

  // Her paket için arka plan renkleri ve ikonlar
  const getPackageColor = (pkg: any) => {
    const type = pkg.type?.toLowerCase() || '1';
    return packageColors[type] || { bg: "bg-gray-500", light: "bg-gray-50", border: "border-gray-200" };
  };

  const getPackageIcon = (pkg: any) => {
    const type = pkg.type?.toLowerCase() || '1';
    return packageIcons[type] || Target;
  };

  // Ders sayısı ve süre gibi bilgileri biçimlendirir
  const formatLessonInfo = (pkg: any) => {
    let info = '';
    
    if (pkg.lessonsInPackage) {
      info += `${pkg.lessonsInPackage} ders`;
    }
    
    if (pkg.lessonDurationMinutes) {
      info += info ? `, ${pkg.lessonDurationMinutes} dk` : `${pkg.lessonDurationMinutes} dk`;
    }
    
    return info || 'Bilgi yok';
  };

  // Özellikleri benzersiz olarak grupla ve sırala
  const featuresList = Array.from(new Set(
    packages.flatMap(pkg => 
      pkg.features?.map((f: string) => f.toLowerCase().trim()) || []
    )
  ));

  return (
    <AnimatePresence>
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      >
        <motion.div 
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ type: "spring", duration: 0.5 }}
          className="bg-white rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col"
        >
          <div className="p-4 sm:p-6 border-b flex justify-between items-center sticky top-0 bg-white z-10">
            <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
              <GitCompare className="w-5 h-5 text-blue-600" />
              Paket Karşılaştırma
            </h3>
            <Button 
              variant="ghost" 
              size="icon"
              className="h-8 w-8 rounded-full" 
              onClick={onClose}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
          
          <div className="overflow-x-auto flex-1 p-4 sm:p-6">
            <div className="min-w-[800px]">
              {/* Başlık ve Paket Türü Satırı */}
              <div className="grid grid-cols-[220px_repeat(auto-fill,minmax(180px,1fr))] gap-4 mb-6">
                <div></div>
                {packages.map((pkg, idx) => {
                  const color = getPackageColor(pkg);
                  const IconComponent = getPackageIcon(pkg);
                  return (
                    <div 
                      key={idx} 
                      className={`${color.light} p-4 rounded-lg text-center border ${color.border} relative shadow-sm`}
                    >
                      <div className={`w-10 h-10 ${color.bg} rounded-full flex items-center justify-center mx-auto mb-2`}>
                        <IconComponent className="w-5 h-5 text-white" />
                      </div>
                      <h4 className="font-bold text-gray-900 mb-1">{pkg.name}</h4>
                      <p className="text-xs text-gray-500">
                        {pkg.type === 'birebir' || pkg.type === '1' ? 'Birebir Ders' : 
                         pkg.type === 'grup' || pkg.type === '2' ? 'Grup Dersi' : 
                         pkg.type === 'video' || pkg.type === '3' ? 'Video Kursu' : 'Standart Kurs'}
                      </p>
                      {pkg.level && (
                        <Badge className="absolute top-2 right-2 text-xs bg-blue-100 text-blue-700">
                          {pkg.level.toUpperCase()}
                        </Badge>
                      )}
                    </div>
                  );
                })}
              </div>
              
              {/* Fiyat ve Seçenekler Satırı */}
              <div className="grid grid-cols-[220px_repeat(auto-fill,minmax(180px,1fr))] gap-4 mb-6">
                <div className="p-4 bg-gray-100 rounded-lg flex items-center">
                  <div>
                    <h5 className="font-bold text-gray-900">Fiyatlandırma</h5>
                    <p className="text-xs text-gray-500">Paket fiyatları ve seçenekler</p>
                  </div>
                </div>
                {packages.map((pkg, idx) => (
                  <div key={`price-${idx}`} className="p-4 bg-white border border-gray-200 rounded-lg">
                    {pkg.options && pkg.options.length > 0 ? (
                      <div>
                        <div className="flex items-baseline">
                          <span className="text-2xl font-bold text-gray-900">₺{pkg.options[0].price}</span>
                          <span className="text-xs text-gray-500 ml-1">'den başlayan</span>
                        </div>
                        
                        <div className="mt-2 space-y-1">
                          {pkg.options.map((option, optIdx) => (
                            <div key={optIdx} className="flex items-center justify-between text-xs">
                              <span>{option.sessions || '1'} ders</span>
                              <div className="flex items-center">
                                <span className="font-medium">₺{option.price}</span>
                                {option.discount && (
                                  <Badge className="ml-1 text-[10px] px-1 py-0 h-4 bg-red-100 text-red-700">
                                    %{option.discount}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                        
                        <div className="mt-2 pt-2 border-t border-gray-100 flex items-center text-xs text-gray-500">
                          <CreditCard className="w-3 h-3 mr-1" />
                          <span>{pkg.options.length} ödeme seçeneği</span>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <div className="flex items-baseline">
                          <span className="text-2xl font-bold text-gray-900">₺{pkg.price}</span>
                        </div>
                        <div className="mt-2 pt-2 border-t border-gray-100 flex items-center text-xs text-gray-500">
                          <CreditCard className="w-3 h-3 mr-1" />
                          <span>Tek fiyat</span>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
              
              {/* Ders Bilgileri */}
              <div className="grid grid-cols-[220px_repeat(auto-fill,minmax(180px,1fr))] gap-4 mb-6">
                <div className="p-4 bg-gray-100 rounded-lg flex items-center">
                  <div>
                    <h5 className="font-bold text-gray-900">Ders Bilgileri</h5>
                    <p className="text-xs text-gray-500">Süre ve içerik detayları</p>
                  </div>
                </div>
                {packages.map((pkg, idx) => (
                  <div key={`lessons-${idx}`} className="p-4 bg-white border border-gray-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Clock className="w-4 h-4 text-gray-500" />
                      <span className="text-sm font-medium">{formatLessonInfo(pkg)}</span>
                    </div>
                    <p className="text-xs text-gray-600 line-clamp-3">{pkg.shortDesc || pkg.description}</p>
                  </div>
                ))}
              </div>
              
              {/* Özellikler Başlığı */}
              <div className="grid grid-cols-[220px_repeat(auto-fill,minmax(180px,1fr))] gap-4 mb-2">
                <div className="p-4 bg-gray-100 rounded-lg flex items-center">
                  <div>
                    <h5 className="font-bold text-gray-900">Kurs Özellikleri</h5>
                    <p className="text-xs text-gray-500">Her paketin içerdiği özellikler</p>
                  </div>
                </div>
                {packages.map((pkg, idx) => (
                  <div key={`features-header-${idx}`} className="p-4 flex items-center justify-center">
                    <div className="text-center text-sm font-medium text-gray-900">
                      {pkg.features?.length || 0} özellik
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Özellikler Listesi */}
              {featuresList.map((feature, featureIdx) => (
                <div 
                  key={`feature-${featureIdx}`} 
                  className={`grid grid-cols-[220px_repeat(auto-fill,minmax(180px,1fr))] gap-4 rounded-lg ${featureIdx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                >
                  <div className="p-3 text-sm text-gray-700">{feature}</div>
                  {packages.map((pkg, pkgIdx) => {
                    const hasFeature = pkg.features?.some((f: string) => f.toLowerCase().trim() === feature);
                    const color = getPackageColor(pkg);
                    
                    return (
                      <div 
                        key={`feature-${featureIdx}-pkg-${pkgIdx}`} 
                        className="p-3 text-center"
                      >
                        {hasFeature ? (
                          <CheckCircle className={`w-5 h-5 text-green-500 mx-auto`} />
                        ) : (
                          <AlertCircle className="w-5 h-5 text-gray-300 mx-auto" />
                        )}
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>
          </div>
          
          <div className="p-4 sm:p-5 border-t bg-gray-50 sticky bottom-0">
            <div className="flex flex-wrap justify-between items-center">
              <p className="text-sm text-gray-500 mb-3 sm:mb-0">
                En uygun paketi seçmek için özellikleri karşılaştırın.
              </p>
              <div className="flex gap-2">
                <Button 
                  variant="outline"
                  onClick={onClose}
                  className="text-sm"
                >
                  İptal
                </Button>
                <Button 
                  onClick={onClose}
                  className="bg-blue-600 hover:bg-blue-700 text-white text-sm"
                >
                  Anladım
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default PackageCompareModal;
