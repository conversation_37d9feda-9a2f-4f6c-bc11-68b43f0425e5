"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, X } from 'lucide-react';
import dynamic from 'next/dynamic';

import { TeacherProfileClientData, ClientPackage as Package, PackageOption } from '@/types/teacher';
import { TeacherProfileHero } from "@/components/teacher/profile/TeacherProfileHero";
import { TeacherStatsBar } from "@/components/teacher/profile/TeacherStatsBar";
import { TeacherTabNavigation } from "@/components/teacher/profile/TeacherTabNavigation";
import { TeacherCoursesTab } from "@/components/teacher/profile/tabs/TeacherCoursesTab";
import { TeacherVideosTab } from "@/components/teacher/profile/tabs/TeacherVideosTab";
import { TeacherReviewsTab } from "@/components/teacher/profile/tabs/TeacherReviewsTab";
import { TeacherAboutTab } from "@/components/teacher/profile/tabs/TeacherAboutTab";
import { FAQTab } from "@/components/teacher/profile/FAQTab";
import { TeacherRecommendations } from "@/components/TeacherRecommendations";
import { TrialLessonModal } from "@/components/modals/TrialLessonModal";

const TeacherBookingCalendarModal = dynamic(
  () => import('@/components/modals/TeacherBookingCalendarModal'),
  { ssr: false }
);

const LessonSelectionModal = dynamic(
  () => import('@/components/modals/LessonSelectionModal').then(mod => mod.LessonSelectionModal),
  {
    ssr: false,
    loading: () => (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white p-8 rounded-2xl shadow-2xl">
          <p className="text-lg font-semibold">Takvim Yükleniyor...</p>
        </div>
      </div>
    ),
  }
);

const VideoPlayerModal = dynamic(
  () => import('@/components/modals/VideoPlayerModal'),
  {
    ssr: false,
    loading: () => (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white p-8 rounded-2xl shadow-2xl">
          <p className="text-lg font-semibold">Video Yükleniyor...</p>
        </div>
      </div>
    ),
  }
);

interface TeacherProfileClientProps {
  teacher: TeacherProfileClientData;
  recommendedTeachers?: any[]; // eslint-disable-line @typescript-eslint/no-explicit-any
}

const TeacherProfileClient: React.FC<TeacherProfileClientProps> = ({ teacher, recommendedTeachers = [] }) => {  const [activeTab, setActiveTab] = useState('courses');
  const [showCalendar, setShowCalendar] = useState(false);
  const [showLessonSelector, setShowLessonSelector] = useState(false);
  const [showTrialModal, setShowTrialModal] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<{ pkg: Package, option: PackageOption } | null>(null);
  const [lessonSelectionConfig, setLessonSelectionConfig] = useState<{
    isMultiLesson: boolean;
    firstLessonFree: boolean;
  }>({ isMultiLesson: true, firstLessonFree: false });
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [showBookingSuccess, setShowBookingSuccess] = useState(false);

  const handleCloseCalendar = () => {
    setShowCalendar(false);
    setSelectedPackage(null);
  };

  const handleVideoPlay = (videoIndex: number) => {
    // İlk önce intro_video_url'i kontrol et ve ekle, sonra videoCourses'dan devam et
    if (videoIndex === 0 && teacher.intro_video_url) {
      // Tanıtım videosu
      setCurrentVideoIndex(0);
    } else {
      // Video kursları
      const adjustedIndex = teacher.intro_video_url ? videoIndex - 1 : videoIndex;
      setCurrentVideoIndex(adjustedIndex);
    }
    setShowVideoModal(true);
  };
  const handlePackageSelect = (pkg: Package, option: PackageOption) => {
    setSelectedPackage({ pkg, option });
    
    // For one-on-one lessons, show the lesson selection modal first
    if (pkg.type?.toLowerCase() === 'birebir' || pkg.type === '1') {
      setShowLessonSelector(true);
    } else {
      // For other types (group, video), show calendar directly
      setShowCalendar(true);
    }
  };
  
  const handleCloseLessonSelector = () => {
    setShowLessonSelector(false);
    setSelectedPackage(null);
  };
  
  const handleProceedToBooking = (
    pkg: Package, 
    option: PackageOption, 
    isMultiLesson: boolean, 
    firstLessonFree: boolean
  ) => {
    setLessonSelectionConfig({
      isMultiLesson,
      firstLessonFree
    });
    setSelectedPackage({ pkg, option });
    setShowLessonSelector(false);
    setShowCalendar(true);
  };
  const handleBookingSuccess = () => {
    setShowBookingSuccess(true);
    setTimeout(() => {
      setShowBookingSuccess(false);
    }, 5000);
  };

  const handleShowTrialModal = () => {
    setShowTrialModal(true);
  };

  const handleCloseTrialModal = () => {
    setShowTrialModal(false);
  };
  const handleTrialBooking = (formData: {
    studentName: string;
    email: string;
    phone: string;
    germanLevel: string;
    learningGoals: string;
    preferredTime: string;
    notes?: string;
  }) => {
    console.log('Trial lesson booking:', formData);
    // Burada API çağrısı yapılacak
    // Şimdilik console.log ile simüle ediyoruz
  };

  useEffect(() => {
    if (showCalendar) {
      document.body.classList.add('modal-open-background-scroll');
    } else {
      document.body.classList.remove('modal-open-background-scroll');
    }
    return () => {
      document.body.classList.remove('modal-open-background-scroll');
    };
  }, [showCalendar]);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'courses':
        return <TeacherCoursesTab teacher={teacher} onPackageSelect={handlePackageSelect} />;
      case 'videos':
        return <TeacherVideosTab teacher={teacher} onVideoPlay={handleVideoPlay} />;
      case 'reviews':
        return <TeacherReviewsTab teacher={teacher} />;
      case 'about':
        return <TeacherAboutTab teacher={teacher} />;
      case 'faq':
        return <FAQTab teacher={teacher} />;
      default:
        return <TeacherCoursesTab teacher={teacher} onPackageSelect={handlePackageSelect} />;
    }
  };

  return (
    <>      <div className="min-h-screen bg-gray-50 w-full overflow-x-hidden" suppressHydrationWarning>
        <TeacherProfileHero 
          teacher={teacher} 
          onShowCalendar={() => setShowCalendar(true)}
          onShowTrialModal={handleShowTrialModal}
        />
        <div className="w-full">
          <TeacherStatsBar teacher={teacher} />
          <TeacherTabNavigation activeTab={activeTab} onTabChange={setActiveTab} />
          
          <main className="w-full lg:max-w-7xl lg:mx-auto py-4 lg:py-8 px-4 sm:px-6 lg:px-8 box-border">
            <div className="bg-white rounded-xl lg:rounded-2xl shadow-sm border border-gray-200 overflow-hidden w-full">
              {renderTabContent()}
            </div>
            
            {/* Recommended Teachers Section */}
            {recommendedTeachers.length > 0 && (
              <div className="mt-8">
                <TeacherRecommendations 
                  currentTeacherId={teacher.id} 
                  teachers={recommendedTeachers} 
                />
              </div>
            )}
          </main>
        </div>
      </div>

      {showCalendar && teacher && (
        <TeacherBookingCalendarModal
          onClose={handleCloseCalendar}
          selectedCourse={selectedPackage ? {
            title: `${selectedPackage.pkg.name}${lessonSelectionConfig.firstLessonFree ? ' (İlk Ders Ücretsiz)' : ''}`,
            price: selectedPackage.option.price || "0"
          } : null}
          teacher={teacher}
          onBookingSuccess={handleBookingSuccess}
        />
      )}

      {showLessonSelector && selectedPackage && teacher && (
        <LessonSelectionModal
          onClose={handleCloseLessonSelector}
          package={selectedPackage.pkg}
          option={selectedPackage.option}
          teacher={teacher}
          onProceedToBooking={handleProceedToBooking}
        />
      )}{showVideoModal && (
        <VideoPlayerModal
          showVideoModal={showVideoModal}
          onClose={() => setShowVideoModal(false)}
          teacher={teacher}
          currentVideoIndex={currentVideoIndex}
          onVideoIndexChange={setCurrentVideoIndex}
          introVideoUrl={teacher.intro_video_url}
        />
      )}

      <AnimatePresence>
        {showBookingSuccess && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.9, opacity: 0, y: 20 }}
              className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full text-center relative"
            >
              <button
                onClick={() => setShowBookingSuccess(false)}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Rezervasyon Başarılı!</h2>
              <p className="text-gray-600 mb-4">
                Randevunuz başarıyla oluşturuldu. Kısa süre içinde size onay e-postası gönderilecektir.
              </p>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <p className="text-green-700 text-sm">
                  <strong>Önemli:</strong> Randevu detaylarınızı e-posta adresinize gönderdik.
                  Lütfen spam klasörünüzü de kontrol edin.
                </p>
              </div>              <p className="text-sm text-gray-400">Bu pencere otomatik olarak kapanacaktır.</p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Trial Lesson Modal */}
      <TrialLessonModal
        isOpen={showTrialModal}
        onClose={handleCloseTrialModal}
        teacher={teacher}
        onBookTrial={handleTrialBooking}
      />
    </>
  );
};

export default TeacherProfileClient;
