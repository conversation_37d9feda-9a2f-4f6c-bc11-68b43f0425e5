import React from 'react';
import { X, Clock, CheckCircle, Target, Users, Video, Calendar, CreditCard, Award, BadgeCheck } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ClientPackage as Package, PackageOption, TeacherProfileClientData } from '@/types/teacher';
import { motion } from 'framer-motion';

interface PackageQuickViewProps {
  isOpen: boolean;
  onClose: () => void;
  pkg: Package;
  onPackageSelect: (pkg: Package, option: PackageOption) => void;
  teacher: TeacherProfileClientData;
}

const PackageQuickView: React.FC<PackageQuickViewProps> = ({ 
  isOpen, 
  onClose, 
  pkg, 
  onPackageSelect,
  teacher
}) => {
  if (!isOpen) return null;

  const packageIcons: { [key: string]: React.ElementType } = {
    '1': Target,
    'birebir': Target,
    '2': Users,
    'grup': Users,
    '3': Video,
    'video': Video
  };

  const IconComponent = packageIcons[pkg.type?.toLowerCase() || pkg.id.toString()] || Target;

  const packageColors: { [key: string]: string } = {
    '1': "from-blue-500 to-blue-600",
    'birebir': "from-blue-500 to-blue-600",
    '2': "from-green-500 to-green-600",
    'grup': "from-green-500 to-green-600",
    '3': "from-purple-500 to-purple-600",
    'video': "from-purple-500 to-purple-600"
  };

  const gradientClass = packageColors[pkg.type?.toLowerCase() || pkg.id.toString()] || "from-gray-500 to-gray-600";
  
  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div 
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        className="bg-white rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col"
      >
        <div className="p-4 sm:p-6 border-b flex justify-between items-center sticky top-0 bg-white z-10">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 bg-gradient-to-br ${gradientClass} rounded-lg flex items-center justify-center shadow-md`}>
              <IconComponent className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-xl font-bold text-gray-900">{pkg.name}</h3>
          </div>
          <Button 
            variant="ghost" 
            size="icon"
            className="h-8 w-8 rounded-full" 
            onClick={onClose}
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="overflow-y-auto p-4 sm:p-6 flex-1">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div className="md:col-span-3">
              <h4 className="font-semibold text-lg mb-3">Kurs Hakkında</h4>
              <p className="text-gray-700 mb-6">{pkg.description}</p>
              
              {pkg.level && (
                <div className="flex items-center gap-2 mb-4">
                  <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                    {pkg.level.toUpperCase()}
                  </Badge>
                  <span className="text-sm text-gray-600">Seviye</span>
                </div>
              )}
              
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h5 className="font-semibold text-gray-900 mb-3">Bu paket şunları içerir:</h5>
                {pkg.features && pkg.features.length > 0 ? (
                  <ul className="space-y-2">
                    {pkg.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-500 italic">Bu kurs için özellik bilgisi bulunmuyor.</p>
                )}
              </div>
              
              {pkg.type?.toLowerCase() === 'grup' && (
                <div className="bg-green-50 rounded-lg p-4 mb-6 border border-green-100">
                  <h5 className="font-semibold text-green-800 flex items-center gap-2 mb-3">
                    <Users className="w-4 h-4" />
                    Grup Dersi Detayları
                  </h5>
                  <div className="space-y-3">
                    {pkg.groupCourseStartDate && (
                      <div className="flex items-start gap-2">
                        <Calendar className="w-4 h-4 text-green-600 mt-0.5" />
                        <div>
                          <span className="text-sm font-medium text-gray-700">Başlangıç Tarihi:</span>
                          <div className="text-sm text-gray-600">
                            {new Date(pkg.groupCourseStartDate).toLocaleDateString('tr-TR', {
                              day: 'numeric',
                              month: 'long',
                              year: 'numeric'
                            })}
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {pkg.groupCourseSchedule && (
                      <div className="flex items-start gap-2">
                        <Clock className="w-4 h-4 text-green-600 mt-0.5" />
                        <div>
                          <span className="text-sm font-medium text-gray-700">Ders Programı:</span>
                          <div className="text-sm text-gray-600">{pkg.groupCourseSchedule}</div>
                        </div>
                      </div>
                    )}
                    
                    {(pkg.enrolledStudentCount !== undefined || pkg.studentCapacity !== undefined) && (
                      <div className="flex items-start gap-2">
                        <Users className="w-4 h-4 text-green-600 mt-0.5" />
                        <div>
                          <span className="text-sm font-medium text-gray-700">Doluluk:</span>
                          <div className="text-sm text-gray-600">
                            {pkg.enrolledStudentCount || 0} / {pkg.studentCapacity || "?"} öğrenci
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                            <div 
                              className="bg-green-500 h-2 rounded-full"
                              style={{ 
                                width: pkg.enrolledStudentCount && pkg.studentCapacity 
                                  ? `${Math.min(100, (parseInt(pkg.enrolledStudentCount.toString()) / parseInt(pkg.studentCapacity)) * 100)}%`
                                  : '0%'
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {pkg.type?.toLowerCase() === 'video' && (
                <div className="bg-purple-50 rounded-lg p-4 mb-6 border border-purple-100">
                  <h5 className="font-semibold text-purple-800 flex items-center gap-2 mb-3">
                    <Video className="w-4 h-4" />
                    Video Kursu Detayları
                  </h5>
                  <div className="grid grid-cols-2 gap-4">
                    {pkg.videoCourseTotalLessons && (
                      <div className="flex items-start gap-2">
                        <Video className="w-4 h-4 text-purple-600 mt-0.5" />
                        <div>
                          <span className="text-sm font-medium text-gray-700">Toplam Video:</span>
                          <div className="text-lg font-semibold text-purple-600">
                            {pkg.videoCourseTotalLessons}
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {pkg.videoCourseTotalHours && (
                      <div className="flex items-start gap-2">
                        <Clock className="w-4 h-4 text-purple-600 mt-0.5" />
                        <div>
                          <span className="text-sm font-medium text-gray-700">Toplam Süre:</span>
                          <div className="text-lg font-semibold text-purple-600">
                            {pkg.videoCourseTotalHours} saat
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {pkg.videoCourseMaterials && (
                    <div className="mt-3 pt-3 border-t border-purple-200">
                      <span className="text-sm font-medium text-gray-700">İçerik & Materyaller:</span>
                      <p className="text-sm text-gray-600 mt-1">{pkg.videoCourseMaterials}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
            
            <div className="md:col-span-2">
              <div className="sticky top-24">
                <div className="bg-gray-50 rounded-xl border border-gray-200 p-4 mb-6">
                  <div className="flex justify-between items-center mb-3">
                    <h5 className="font-semibold">Öğretmen</h5>
                    <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                      {teacher.rating}⭐
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-3 mb-4">
                    <img 
                      src={teacher.avatar} 
                      alt={teacher.name} 
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <h6 className="font-medium">{teacher.name}</h6>
                      <p className="text-xs text-gray-600">{teacher.shortBio}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 text-center mb-4">
                    <div className="bg-white p-2 rounded border border-gray-200">
                      <div className="text-lg font-bold text-blue-600">{teacher.completedLessons || 0}</div>
                      <div className="text-xs text-gray-600">Ders</div>
                    </div>
                    <div className="bg-white p-2 rounded border border-gray-200">
                      <div className="text-lg font-bold text-green-600">{teacher.activeStudentCount || 0}</div>
                      <div className="text-xs text-gray-600">Öğrenci</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-center gap-2 mb-1">
                    <Award className="w-4 h-4 text-amber-500" />
                    <span className="text-sm">{teacher.experienceYears || '1+'} yıl deneyim</span>
                  </div>
                </div>
                
                <div className="rounded-xl border border-gray-200 overflow-hidden">
                  <div className="bg-gray-50 p-4 border-b border-gray-100">
                    <h5 className="font-semibold">Kurs Seçenekleri</h5>
                  </div>
                  
                  <div className="p-4">
                    {pkg.options && pkg.options.length > 0 ? (
                      <div className="space-y-4">
                        {pkg.options.map((option, index) => (
                          <div 
                            key={index} 
                            className="border border-gray-200 rounded-lg p-3 hover:border-blue-300 hover:shadow-sm transition-all duration-200"
                          >
                            <div className="flex justify-between mb-2 items-center">
                              <div className="flex items-center gap-1">
                                <span className="font-semibold">{option.sessions || option.name} Ders</span>
                                {option.duration && (
                                  <span className="text-xs text-gray-500 flex items-center">
                                    <Clock className="w-3 h-3 mr-0.5 inline" />
                                    {option.duration}
                                  </span>
                                )}
                              </div>
                              {option.discount && (
                                <Badge className="bg-red-100 text-red-700">%{option.discount}</Badge>
                              )}
                            </div>
                            
                            <div className="flex items-end justify-between mb-2">
                              <div>
                                <span className="text-2xl font-bold">₺{option.price}</span>
                                {option.perLesson && (
                                  <span className="text-sm text-gray-500 ml-1">
                                    (₺{option.perLesson}/ders)
                                  </span>
                                )}
                              </div>
                            </div>
                            
                            <Button
                              onClick={() => onPackageSelect(pkg, option)}
                              className={`bg-gradient-to-r ${gradientClass} text-white w-full shadow hover:shadow-md transition-all`}
                            >
                              <CreditCard className="w-4 h-4 mr-2" />
                              Seçimi Tamamla
                            </Button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-end justify-between mb-3">
                          <span className="text-2xl font-bold">₺{pkg.price}</span>
                        </div>
                        
                        <Button
                          onClick={() => onPackageSelect(pkg, { price: pkg.price })}
                          className={`bg-gradient-to-r ${gradientClass} text-white w-full shadow hover:shadow-md transition-all`}
                        >
                          <CreditCard className="w-4 h-4 mr-2" />
                          Seçimi Tamamla
                        </Button>
                      </div>
                    )}
                    
                    <div className="mt-4 flex items-center justify-center">
                      <BadgeCheck className="w-4 h-4 text-green-500 mr-1" />
                      <span className="text-xs text-gray-500">Güvenli Ödeme</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default PackageQuickView;
