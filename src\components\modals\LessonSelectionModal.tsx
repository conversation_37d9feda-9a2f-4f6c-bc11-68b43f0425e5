import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X, CheckCircle, CreditCard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { TeacherProfileClientData, ClientPackage, PackageOption } from '@/types/teacher';
import { SingleOrMultiLessonSelector } from '@/components/teacher/profile/SingleOrMultiLessonSelector';

interface LessonSelectionModalProps {
  onClose: () => void;
  package: ClientPackage;
  option: PackageOption;
  teacher: TeacherProfileClientData;
  onProceedToBooking: (pkg: ClientPackage, option: PackageOption, isMultiLesson: boolean, firstLessonFree: boolean) => void;
}

export const LessonSelectionModal: React.FC<LessonSelectionModalProps> = ({
  onClose, 
  package: pkg, 
  option, 
  teacher, 
  onProceedToBooking 
}) => {
  const [isMultiLesson, setIsMultiLesson] = useState(true);
  const [firstLessonFree, setFirstLessonFree] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  // Handle clicking outside to close modal
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // Package price calculation
  const lessonPrice = typeof option.price === 'string' ? parseFloat(option.price) : option.price;
  const sessionsCount = option.sessions || 4;

  // Handle booking continuation
  const handleProceedToBooking = () => {
    onProceedToBooking(pkg, option, isMultiLesson, firstLessonFree);
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div 
        ref={modalRef}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-2xl shadow-xl w-full max-w-md overflow-hidden"
      >
        <div className="p-5 border-b flex justify-between items-center">
          <h3 className="text-lg font-bold text-gray-900">Ders Seçenekleri</h3>
          <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="p-5">
          <div className="mb-5">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                <CreditCard className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h4 className="text-lg font-semibold text-gray-900">{pkg.name}</h4>
                <p className="text-sm text-gray-600">{teacher.name} ile özel dersler</p>
              </div>
            </div>
            
            <p className="text-sm text-gray-700 mb-4">{pkg.description}</p>
            
            <SingleOrMultiLessonSelector 
              isMultiLesson={isMultiLesson}
              firstLessonFree={firstLessonFree}
              onMultiLessonChange={setIsMultiLesson}
              onFirstLessonFreeChange={setFirstLessonFree}
              lessonPrice={lessonPrice}
              sessionsCount={sessionsCount}
            />
          </div>

          <div className="space-y-3 border-t pt-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-700 font-medium">Toplam:</span>
              <span className="text-xl font-bold text-gray-900">
                ₺{isMultiLesson 
                  ? firstLessonFree 
                    ? lessonPrice * (sessionsCount - 1) 
                    : lessonPrice * sessionsCount
                  : firstLessonFree ? 0 : lessonPrice}
              </span>
            </div>
            
            {(isMultiLesson || (!isMultiLesson && !firstLessonFree)) && (
              <div className="text-sm text-gray-500 flex items-center gap-1.5">
                <CheckCircle className="w-4 h-4 text-green-600" />
                Güvenli ödeme sistemi
              </div>
            )}
            
            <Button 
              onClick={handleProceedToBooking} 
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              {firstLessonFree && !isMultiLesson ? 'Ücretsiz Ders Rezervasyonu Yap' : 'Rezervasyon Yap'}
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
