import React from 'react';
import { BookOpen, GraduationCap, Award, Globe, CheckCircle, Phone, Mail, MapPin, Clock, Zap } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { TeacherProfileClientData } from '@/types/teacher';

interface TeacherAboutTabProps {
  teacher: TeacherProfileClientData;
}

export const TeacherAboutTab: React.FC<TeacherAboutTabProps> = ({ teacher }) => {
  // The bio might be a single string with newlines. Split it into paragraphs.
  const bioParagraphs = teacher.bio?.split('\n').filter(p => p.trim() !== '') || [];

  return (
    <div className="grid lg:grid-cols-3 gap-6 lg:gap-8 p-4 md:p-6">
      <div className="lg:col-span-2 space-y-6">
        <Card className="bg-white border border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-800">
              <BookOpen className="w-6 h-6 text-blue-500" />
              Hakkımda
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {bioParagraphs.length > 0 ? (
              bioParagraphs.map((paragraph, index) => (
                <p key={index} className="text-gray-700 leading-relaxed">{paragraph}</p>
              ))
            ) : (
              <p className="text-gray-500">Öğretmen henüz bir biyografi eklememiş.</p>
            )}
            
            {teacher.specializations && teacher.specializations.length > 0 && (
              <div className="mt-6">
                <h3 className="font-semibold text-gray-900 mb-3">Uzmanlık Alanlarım</h3>
                <div className="flex flex-wrap gap-2">
                  {teacher.specializations.map((specialty, index) => (
                    <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-800">
                      {specialty}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {teacher.education && teacher.education.length > 0 && (
          <Card className="bg-white border border-gray-200 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-gray-800">
                <GraduationCap className="w-6 h-6 text-blue-500" />
                Eğitim
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {teacher.education.map((edu, index) => (
                  <div key={index} className="border-l-4 border-blue-500 pl-4">
                    <h4 className="font-semibold text-gray-900">{edu.degree}</h4>
                    <p className="text-gray-600">{edu.institution}</p>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <MapPin className="w-4 h-4" />
                      <span>{edu.university}</span>
                      <span>•</span>
                      <span>{edu.year}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {teacher.certificates && teacher.certificates.length > 0 && (
          <Card className="bg-white border border-gray-200 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-gray-800">
                <Award className="w-6 h-6 text-purple-500" />
                Sertifikalar
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {teacher.certificates.map((cert, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900">{cert.name}</h4>
                    <p className="text-gray-600">{cert.issuer}</p>
                    <span className="text-sm text-gray-500">{cert.year}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="space-y-6">
        <Card className="bg-white border border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-800">
              <Globe className="w-5 h-5 text-blue-500" />
              Diller
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {teacher.nativeLanguage && (
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-gray-700 font-medium">{teacher.nativeLanguage} (Ana dil)</span>
                </li>
              )}
              {teacher.languages?.filter(lang => lang !== teacher.nativeLanguage).map((lang, index) => (
                <li key={index} className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-gray-700">{lang}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card className="bg-white border border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-800">
              <Phone className="w-5 h-5 text-blue-500" />
              İletişim
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {teacher.email && (
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                  <Mail className="w-5 h-5 text-blue-500" />
                  <span className="text-gray-700 font-medium">{teacher.email}</span>
                </div>
              )}
              {teacher.timezone && (
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <Clock className="w-5 h-5 text-green-500" />
                  <span className="text-gray-700 font-medium">{teacher.timezone}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>        <Card className="bg-gradient-to-br from-yellow-500 via-orange-500 to-red-500 text-white shadow-xl border-0 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
          <CardContent className="p-6 relative z-10">
            <div className="flex items-center gap-2 mb-3">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                <Zap className="w-5 h-5" />
              </div>
              <div>
                <h3 className="text-lg font-bold">Ücretsiz Deneme Dersi</h3>
                <span className="bg-white/20 px-2 py-1 rounded text-xs font-semibold">%100 İNDİRİM</span>
              </div>
            </div>
            <p className="mb-4 text-white/90 text-sm leading-relaxed">
              15 dakikalık ücretsiz tanışma dersi ile Almanca seviyenizi belirleyin, 
              öğretmeninizle tanışın ve size özel öğrenim planınızı oluşturun.
            </p>
            <div className="space-y-2 mb-4">
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <span>Seviye belirleme testi</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <span>Kişiselleştirilmiş öğrenim planı</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <span>Öğretmen uyumluluk kontrolü</span>
              </div>
            </div>
            <Button className="w-full bg-white text-orange-600 hover:bg-gray-100 font-bold text-base py-3 rounded-lg shadow-lg">
              <Zap className="w-5 h-5 mr-2" />
              Hemen Başla - Tamamen Ücretsiz
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
