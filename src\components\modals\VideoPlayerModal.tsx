import React from 'react';
import { Video, ChevronLeft, ChevronRight } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { TeacherProfileClientData } from '@/types/teacher';
import { AspectRatio } from '../ui/aspect-ratio';

interface VideoPlayerModalProps {
  showVideoModal: boolean;
  onClose: () => void;
  teacher: TeacherProfileClientData;
  currentVideoIndex: number;
  onVideoIndexChange: (index: number) => void;
  introVideoUrl?: string; // Öğretmenin tanıtım videosu URL'i
}

export const VideoPlayerModal: React.FC<VideoPlayerModalProps> = ({
  showVideoModal,
  onClose,
  teacher,
  currentVideoIndex,
  onVideoIndexChange,
  introVideoUrl
}) => {
  // Eğer currentVideoIndex 0 ise ve tanıtım videosu varsa, onu göster
  const isIntroVideo = currentVideoIndex === 0 && !!introVideoUrl;
  // Diğer durumlarda normal video kurslarına bak
  const currentVideo = !isIntroVideo ? teacher.videoCourses?.[introVideoUrl ? currentVideoIndex - 1 : currentVideoIndex] : null;

  const getYouTubeEmbedUrl = (url: string | undefined) => {
    if (!url) return null;
    try {
      const urlObj = new URL(url);
      const videoId = urlObj.searchParams.get('v');
      if (videoId) {
        return `https://www.youtube.com/embed/${videoId}?autoplay=1`;
      }
      // Handle youtu.be links
      if (urlObj.hostname === 'youtu.be') {
        return `https://www.youtube.com/embed/${urlObj.pathname.slice(1)}?autoplay=1`;
      }
    } catch {
      console.error("Invalid YouTube URL:", url);
      return null;
    }
    return null;
  };
  // Tanıtım videosu veya kurs videosu URL'ini belirle
  const videoUrl = isIntroVideo 
    ? introVideoUrl 
    : currentVideo?.youtubePlaylistUrl || undefined;
    
  const embedUrl = getYouTubeEmbedUrl(videoUrl);

  return (
    <Dialog open={showVideoModal} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl p-4 sm:p-6">
        <DialogHeader>          <DialogTitle className="flex items-center gap-2 text-gray-800">
            <Video className="w-5 h-5 text-blue-500" />
            {isIntroVideo 
              ? `${teacher.name} - Tanıtım Videosu` 
              : currentVideo?.name || 'Video Ders'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="w-full">
          <AspectRatio ratio={16 / 9} className="bg-black rounded-lg overflow-hidden">
            {embedUrl ? (
              <iframe
                src={embedUrl}
                title={isIntroVideo 
                  ? `${teacher.name} - Tanıtım Videosu` 
                  : currentVideo?.name || 'YouTube video player'}
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
                className="w-full h-full"
              ></iframe>
            ) : (
              <div className="w-full h-full flex items-center justify-center text-white bg-gray-800">
                <p>Video yüklenemedi.</p>
              </div>
            )}
          </AspectRatio>
        </div>
        
        <div className="flex gap-2 justify-center mt-4">          <Button
            variant="outline"
            size="sm"
            onClick={() => onVideoIndexChange(Math.max(0, currentVideoIndex - 1))}
            disabled={currentVideoIndex === 0}
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Önceki
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const totalVideos = (introVideoUrl ? 1 : 0) + (teacher.videoCourses?.length || 0);
              onVideoIndexChange(Math.min(totalVideos - 1, currentVideoIndex + 1));
            }}
            disabled={currentVideoIndex === ((introVideoUrl ? 1 : 0) + (teacher.videoCourses?.length || 0) - 1)}
          >
            Sonraki
            <ChevronRight className="w-4 h-4 ml-1" />
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default VideoPlayerModal;
