import React from 'react';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Info } from "lucide-react";

interface SingleOrMultiLessonSelectorProps {
  isMultiLesson: boolean;
  firstLessonFree: boolean;
  onMultiLessonChange: (isMulti: boolean) => void;
  onFirstLessonFreeChange: (isFree: boolean) => void;
  lessonPrice: number;
  sessionsCount?: number;
}

export const SingleOrMultiLessonSelector: React.FC<SingleOrMultiLessonSelectorProps> = ({
  isMultiLesson,
  firstLessonFree,
  onMultiLessonChange,
  onFirstLessonFreeChange,
  lessonPrice,
  sessionsCount = 4
}) => {
  const calculateDiscount = () => {
    let totalPrice = isMultiLesson ? lessonPrice * sessionsCount : lessonPrice;
    if (firstLessonFree && isMultiLesson) {
      totalPrice -= lessonPrice;
    } else if (firstLessonFree) {
      totalPrice = 0;
    }
    
    const regularPrice = isMultiLesson ? lessonPrice * sessionsCount : lessonPrice;
    const discountAmount = regularPrice - totalPrice;
    const discountPercentage = Math.round((discountAmount / regularPrice) * 100);
    
    return {
      discountAmount,
      discountPercentage,
      totalPrice
    };
  };

  const {discountAmount, discountPercentage, totalPrice} = calculateDiscount();

  return (
    <div className="space-y-4">
      <div className="border rounded-lg p-4 space-y-3 bg-gray-50">
        <h4 className="font-medium text-gray-900 flex items-center gap-2">
          Ders Seçenekleri
          <Badge className="bg-blue-100 text-blue-700 border-blue-200">Yeni</Badge>
        </h4>
        
        <RadioGroup defaultValue={isMultiLesson ? "multi" : "single"} className="space-y-2" 
          onValueChange={(value) => onMultiLessonChange(value === "multi")}>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="single" id="single-lesson" />
            <Label htmlFor="single-lesson" className="font-medium cursor-pointer">
              Tek Ders
              <span className="block text-sm text-gray-500 font-normal mt-0.5">
                {firstLessonFree ? "Ücretsiz ilk tanışma dersi" : `₺${lessonPrice}`}
              </span>
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="multi" id="multi-lesson" />
            <Label htmlFor="multi-lesson" className="font-medium cursor-pointer">
              {sessionsCount} Derslik Paket
              <span className="block text-sm text-gray-500 font-normal mt-0.5">
                {firstLessonFree 
                  ? `₺${totalPrice} (İlk ders ücretsiz)` 
                  : `₺${totalPrice} (Ders başı ₺${(totalPrice / sessionsCount).toFixed(0)})`}
              </span>
            </Label>
          </div>
        </RadioGroup>
        
        <div className="pt-2 border-t">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="first-lesson-free" 
              checked={firstLessonFree} 
              onCheckedChange={(checked) => onFirstLessonFreeChange(checked === true)}
            />
            <Label htmlFor="first-lesson-free" className="text-sm font-medium cursor-pointer">
              İlk dersim ücretsiz olsun
              {discountPercentage > 0 && (
                <Badge className="ml-2 bg-red-100 text-red-700 border-red-200">
                  %{discountPercentage} Tasarruf
                </Badge>
              )}
            </Label>
          </div>
          {firstLessonFree && (
            <div className="flex items-start mt-2 pl-6 text-xs text-gray-500 gap-1">
              <Info className="w-3.5 h-3.5 mt-0.5 flex-shrink-0 text-blue-500" />
              <p>
                İlk tanışma dersinizi ücretsiz alarak öğretmenimizle uyumunuzu test edebilir,
                dil seviyenizi ölçtürüp size özel bir öğrenim planı oluşturabilirsiniz.
              </p>
            </div>
          )}
        </div>
      </div>
      
      {discountAmount > 0 && (
        <div className="text-right text-sm">
          <span className="text-gray-500">Toplam Tasarruf: </span>
          <span className="font-semibold text-green-600">₺{discountAmount}</span>
        </div>
      )}
    </div>
  );
};
