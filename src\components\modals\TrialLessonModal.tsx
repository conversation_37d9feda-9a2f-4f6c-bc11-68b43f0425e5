import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Zap, CheckCircle, Calendar, User, Star, Quote } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { TeacherProfileClientData } from '@/types/teacher';
import Image from 'next/image';

interface TrialLessonModalProps {
  isOpen: boolean;
  onClose: () => void;
  teacher: TeacherProfileClientData;
  onBookTrial: (formData: TrialBookingData) => void;
}

interface TrialBookingData {
  studentName: string;
  email: string;
  phone: string;
  germanLevel: string;
  learningGoals: string;
  preferredTime: string;
  notes?: string;
}

export const TrialLessonModal: React.FC<TrialLessonModalProps> = ({
  isOpen,
  onClose,
  teacher,
  onBookTrial
}) => {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<TrialBookingData>({
    studentName: '',
    email: '',
    phone: '',
    germanLevel: '',
    learningGoals: '',
    preferredTime: '',
    notes: ''
  });

  const germanLevels = [
    { value: 'absolute-beginner', label: 'Hiç bilmiyorum (Absolute Beginner)' },
    { value: 'a1', label: 'A1 - Temel seviye' },
    { value: 'a2', label: 'A2 - Alt orta seviye' },
    { value: 'b1', label: 'B1 - Orta seviye' },
    { value: 'b2', label: 'B2 - Üst orta seviye' },
    { value: 'c1', label: 'C1 - İleri seviye' },
    { value: 'c2', label: 'C2 - Anadil seviyesi' },
    { value: 'not-sure', label: 'Emin değilim' }
  ];

  const learningGoals = [
    'Günlük konuşma yapabilmek',
    'İş dünyasında kullanmak',
    'Almanya\'ya taşınma hazırlığı',
    'Üniversite/eğitim için',
    'Sınav hazırlığı (TestDaF, DSH, vb.)',
    'Hobi olarak öğrenmek',
    'Ailemle iletişim kurmak',
    'Seyahat için'
  ];

  const timeSlots = [
    'Hafta içi sabah (09:00-12:00)',
    'Hafta içi öğle (12:00-17:00)',
    'Hafta içi akşam (17:00-21:00)',
    'Hafta sonu sabah (09:00-12:00)',
    'Hafta sonu öğle (12:00-17:00)',
    'Hafta sonu akşam (17:00-21:00)',
    'Esnek - Öğretmenle konuşarak belirleriz'
  ];

  const handleSubmit = () => {
    onBookTrial(formData);
    setStep(3); // Başarı sayfasına geç
  };

  const trialBenefits = [
    {
      icon: User,
      title: 'Öğretmeninizle Tanışın',
      description: 'Öğretim tarzını ve kişiliğini tanıyın'
    },
    {
      icon: Star,
      title: 'Seviyenizi Belirleyin',
      description: 'Mevcut Almanca seviyenizi profesyonel değerlendirme'
    },
    {
      icon: CheckCircle,
      title: 'Özel Plan Oluşturun',
      description: 'Size özel öğrenim planı ve hedef belirleme'
    },
    {
      icon: Zap,
      title: 'Tamamen Ücretsiz',
      description: 'Hiçbir ödeme yapmadan deneyimleyin'
    }
  ];

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white p-6 rounded-t-2xl relative overflow-hidden">
            <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
            <div className="relative z-10">
              <button
                onClick={onClose}
                className="absolute top-0 right-0 w-8 h-8 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
              
              <div className="flex items-center gap-3 mb-2">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <Zap className="w-6 h-6" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold">Ücretsiz Deneme Dersi</h2>
                  <p className="text-white/90">15 dakikalık tanışma seansı</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Badge className="bg-white/20 text-white border-white/20">
                  %100 ÜCRETSİZ
                </Badge>
                <Badge className="bg-white/20 text-white border-white/20">
                  15 DAKİKA
                </Badge>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {step === 1 && (
              <div className="space-y-6">                {/* Öğretmen Bilgisi */}
                <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-xl">
                  <Image
                    src={teacher.avatar || '/placeholder.svg'}
                    alt={teacher.name}
                    width={64}
                    height={64}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                  <div>
                    <h3 className="font-bold text-lg">{teacher.name}</h3>
                    <p className="text-gray-600">{teacher.title}</p>
                    <div className="flex items-center gap-1 mt-1">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-medium">{teacher.average_rating || 5.0}</span>
                      <span className="text-sm text-gray-500">({teacher.totalReviews || 0} değerlendirme)</span>
                    </div>
                  </div>
                </div>

                {/* Faydalar */}
                <div>
                  <h4 className="font-semibold text-lg mb-4">Deneme Dersinizde Neler Olacak?</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {trialBenefits.map((benefit, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                        <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0">
                          <benefit.icon className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <h5 className="font-medium text-sm">{benefit.title}</h5>
                          <p className="text-xs text-gray-600">{benefit.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Testimonial */}
                <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
                  <div className="flex items-start gap-3">
                    <Quote className="w-5 h-5 text-blue-500 flex-shrink-0 mt-1" />
                    <div>                      <p className="text-sm italic text-gray-700">
                        &ldquo;Deneme dersi sayesinde öğretmenimle uyumumuzu gördük ve güvenle başladık. 
                        Seviyemi doğru belirledi ve bana uygun bir plan hazırladı.&rdquo;
                      </p>
                      <p className="text-xs text-gray-500 mt-2">- Ahmet K., A2 Seviyesi</p>
                    </div>
                  </div>
                </div>

                <Button
                  onClick={() => setStep(2)}
                  className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-bold py-3"
                >
                  Deneme Dersini Rezerve Et
                  <Calendar className="w-5 h-5 ml-2" />
                </Button>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-center">Hemen Rezerve Edelim</h3>
                
                <div className="space-y-4">
                  {/* Kişisel Bilgiler */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Adınız Soyadınız *</label>
                      <Input
                        value={formData.studentName}
                        onChange={(e) => setFormData({...formData, studentName: e.target.value})}
                        placeholder="Örn: Ahmet Yılmaz"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">E-posta *</label>
                      <Input
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Telefon Numarası *</label>
                    <Input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      placeholder="+90 555 123 45 67"
                      required
                    />
                  </div>

                  {/* Almanca Seviyesi */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Mevcut Almanca Seviyeniz *</label>
                    <select
                      value={formData.germanLevel}
                      onChange={(e) => setFormData({...formData, germanLevel: e.target.value})}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500"
                      required
                    >
                      <option value="">Seviyenizi seçin</option>
                      {germanLevels.map((level) => (
                        <option key={level.value} value={level.value}>
                          {level.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Öğrenme Hedefleri */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Almanca Öğrenme Hedefiniz *</label>
                    <select
                      value={formData.learningGoals}
                      onChange={(e) => setFormData({...formData, learningGoals: e.target.value})}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500"
                      required
                    >
                      <option value="">Hedefinizi seçin</option>
                      {learningGoals.map((goal) => (
                        <option key={goal} value={goal}>
                          {goal}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Tercih Edilen Zaman */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Ders Saati Tercihiniz *</label>
                    <select
                      value={formData.preferredTime}
                      onChange={(e) => setFormData({...formData, preferredTime: e.target.value})}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500"
                      required
                    >
                      <option value="">Uygun zamanı seçin</option>
                      {timeSlots.map((slot) => (
                        <option key={slot} value={slot}>
                          {slot}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Ek Notlar */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Ek Notlarınız (İsteğe bağlı)</label>
                    <Textarea
                      value={formData.notes}
                      onChange={(e) => setFormData({...formData, notes: e.target.value})}
                      placeholder="Öğretmeninize iletmek istediğiniz özel durumlar, sorular..."
                      rows={3}
                    />
                  </div>
                </div>

                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setStep(1)}
                    className="flex-1"
                  >
                    Geri
                  </Button>
                  <Button
                    onClick={handleSubmit}
                    disabled={!formData.studentName || !formData.email || !formData.phone || !formData.germanLevel || !formData.learningGoals || !formData.preferredTime}
                    className="flex-1 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-bold"
                  >
                    Rezervasyonu Tamamla
                    <Zap className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
            )}

            {step === 3 && (
              <div className="text-center space-y-6">
                <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <CheckCircle className="w-10 h-10 text-green-600" />
                </div>
                
                <div>
                  <h3 className="text-2xl font-bold text-green-600 mb-2">Rezervasyon Başarılı!</h3>
                  <p className="text-gray-600">
                    Deneme dersi rezervasyonunuz başarıyla oluşturuldu. {teacher.name} 
                    ile 24 saat içinde iletişime geçeceğiz.
                  </p>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-semibold mb-2">Sonraki Adımlar:</h4>
                  <ul className="text-sm text-gray-600 space-y-1 text-left">
                    <li>✅ E-posta ile onay mesajı alacaksınız</li>
                    <li>✅ Öğretmeniniz uygun tarihleri paylaşacak</li>
                    <li>✅ Zoom linki ders saatinden önce gönderilecek</li>
                    <li>✅ Ders materyalleri hazırlanacak</li>
                  </ul>
                </div>

                <Button
                  onClick={onClose}
                  className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold"
                >
                  Tamam
                </Button>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
