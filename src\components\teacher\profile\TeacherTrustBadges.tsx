import React from 'react';
import { Shield, Award, GraduationCap, Clock } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { TeacherProfileClientData } from '@/types/teacher';

interface TeacherTrustBadgesProps {
  teacher: TeacherProfileClientData;
  className?: string;
}

export const TeacherTrustBadges: React.FC<TeacherTrustBadgesProps> = ({ teacher, className }) => {
  // Güven rozetleri statik olarak tanımlanmış
  const trustBadges = [
    {
      id: 'verified',
      isActive: true, // Varsayılan olarak aktif, gerçek veri ile değiştirilecek
      title: 'Doğrulanmış Öğretmen',
      description: 'Kimlik ve yeterlilikleri doğrulanmıştır',
      icon: Shield,
      color: 'text-green-600 bg-green-50 border-green-100'
    },
    {
      id: 'certified',
      isActive: teacher.certificates && teacher.certificates.length > 0,
      title: 'Sertifikalı Öğretmen',
      description: 'Eğitimci sertifikalarını yüklemiştir',
      icon: Award,
      color: 'text-blue-600 bg-blue-50 border-blue-100'
    },
    {
      id: 'experienced',
      isActive: teacher.experienceYears && parseInt(teacher.experienceYears) >= 2, // 2 yıl veya daha fazla deneyim
      title: 'Deneyimli Öğretmen',
      description: '2+ yıl öğretmenlik deneyimi vardır',
      icon: Clock,
      color: 'text-purple-600 bg-purple-50 border-purple-100'
    },
    {
      id: 'superteacher',
      isActive: teacher.totalLessons && teacher.totalLessons >= 1000,
      title: 'Süper Öğretmen',
      description: '1000+ ders vermiştir',
      icon: GraduationCap,
      color: 'text-amber-600 bg-amber-50 border-amber-100'
    }
  ];

  const activeBadges = trustBadges.filter(badge => badge.isActive);

  if (activeBadges.length === 0) {
    return null;
  }

  return (
    <div className={cn("flex flex-wrap gap-2 items-center justify-center lg:justify-start", className)}>
      {activeBadges.map((badge) => (
        <TooltipProvider key={badge.id} delayDuration={200}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge className={cn("px-2 py-1 h-auto rounded-md flex items-center gap-1.5 shadow-sm cursor-help", badge.color)}>
                <badge.icon className="w-3.5 h-3.5" />
                <span className="text-xs font-medium">{badge.title}</span>
              </Badge>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p className="text-sm">{badge.description}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ))}
    </div>
  );
};
